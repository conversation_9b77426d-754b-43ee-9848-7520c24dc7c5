package com.library.dao;

import com.library.model.Fine;
import com.library.util.DatabaseConnection;

import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Data Access Object for Fine operations
 */
public class FineDAO {
    private static final Logger LOGGER = Logger.getLogger(FineDAO.class.getName());
    private final DatabaseConnection dbConnection;

    public FineDAO() {
        this.dbConnection = DatabaseConnection.getInstance();
    }

    /**
     * Create a new fine
     */
    public boolean createFine(Fine fine) {
        String sql = "INSERT INTO fines (borrow_id, user_id, amount, reason, fine_date, status) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setInt(1, fine.getBorrowId());
            stmt.setInt(2, fine.getUserId());
            stmt.setBigDecimal(3, fine.getAmount());
            stmt.setString(4, fine.getReason());
            stmt.setDate(5, Date.valueOf(fine.getFineDate()));
            stmt.setString(6, fine.getStatus().name());
            
            int rowsAffected = stmt.executeUpdate();
            
            if (rowsAffected > 0) {
                try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        fine.setFineId(generatedKeys.getInt(1));
                    }
                }
                LOGGER.info("Fine created successfully for user: " + fine.getUserId());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error creating fine", e);
        }
        return false;
    }

    /**
     * Get fine by ID
     */
    public Fine getFineById(int fineId) {
        String sql = "SELECT f.*, u.username, u.first_name, u.last_name, b.title " +
                    "FROM fines f " +
                    "JOIN users u ON f.user_id = u.user_id " +
                    "JOIN borrow_records br ON f.borrow_id = br.borrow_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE f.fine_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, fineId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToFine(rs);
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting fine by ID: " + fineId, e);
        }
        return null;
    }

    /**
     * Update fine
     */
    public boolean updateFine(Fine fine) {
        String sql = "UPDATE fines SET amount = ?, reason = ?, paid_date = ?, status = ? " +
                    "WHERE fine_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setBigDecimal(1, fine.getAmount());
            stmt.setString(2, fine.getReason());
            stmt.setDate(3, fine.getPaidDate() != null ? 
                        Date.valueOf(fine.getPaidDate()) : null);
            stmt.setString(4, fine.getStatus().name());
            stmt.setInt(5, fine.getFineId());
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                LOGGER.info("Fine updated successfully: " + fine.getFineId());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating fine: " + fine.getFineId(), e);
        }
        return false;
    }

    /**
     * Get user's fines
     */
    public List<Fine> getUserFines(int userId, String status) {
        List<Fine> fines = new ArrayList<>();
        StringBuilder sql = new StringBuilder(
            "SELECT f.*, u.username, u.first_name, u.last_name, b.title " +
            "FROM fines f " +
            "JOIN users u ON f.user_id = u.user_id " +
            "JOIN borrow_records br ON f.borrow_id = br.borrow_id " +
            "JOIN books b ON br.book_id = b.book_id " +
            "WHERE f.user_id = ?"
        );
        
        if (status != null && !status.isEmpty()) {
            sql.append(" AND f.status = ?");
        }
        
        sql.append(" ORDER BY f.fine_date DESC");
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql.toString())) {
            
            stmt.setInt(1, userId);
            if (status != null && !status.isEmpty()) {
                stmt.setString(2, status);
            }
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    fines.add(mapResultSetToFine(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting user fines: " + userId, e);
        }
        return fines;
    }

    /**
     * Get all pending fines
     */
    public List<Fine> getPendingFines(int offset, int limit) {
        List<Fine> fines = new ArrayList<>();
        String sql = "SELECT f.*, u.username, u.first_name, u.last_name, b.title " +
                    "FROM fines f " +
                    "JOIN users u ON f.user_id = u.user_id " +
                    "JOIN borrow_records br ON f.borrow_id = br.borrow_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE f.status = 'PENDING' ORDER BY f.fine_date DESC LIMIT ? OFFSET ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, limit);
            stmt.setInt(2, offset);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    fines.add(mapResultSetToFine(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting pending fines", e);
        }
        return fines;
    }

    /**
     * Get user's total outstanding fine amount
     */
    public BigDecimal getUserOutstandingFineAmount(int userId) {
        String sql = "SELECT COALESCE(SUM(amount), 0) FROM fines WHERE user_id = ? AND status = 'PENDING'";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getBigDecimal(1);
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting user outstanding fine amount: " + userId, e);
        }
        return BigDecimal.ZERO;
    }

    /**
     * Mark fine as paid
     */
    public boolean markFineAsPaid(int fineId) {
        String sql = "UPDATE fines SET status = 'PAID', paid_date = CURDATE() WHERE fine_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, fineId);
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                LOGGER.info("Fine marked as paid: " + fineId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error marking fine as paid: " + fineId, e);
        }
        return false;
    }

    /**
     * Waive fine (admin only)
     */
    public boolean waiveFine(int fineId) {
        String sql = "UPDATE fines SET status = 'WAIVED', paid_date = CURDATE() WHERE fine_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, fineId);
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                LOGGER.info("Fine waived: " + fineId);
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error waiving fine: " + fineId, e);
        }
        return false;
    }

    /**
     * Calculate fine amount for overdue days
     */
    public BigDecimal calculateFineAmount(long overdueDays) {
        // $1.00 per day overdue, with a maximum of $50.00
        BigDecimal dailyFine = new BigDecimal("1.00");
        BigDecimal maxFine = new BigDecimal("50.00");
        
        BigDecimal calculatedFine = dailyFine.multiply(BigDecimal.valueOf(overdueDays));
        
        return calculatedFine.compareTo(maxFine) > 0 ? maxFine : calculatedFine;
    }

    /**
     * Get fine statistics
     */
    public Object[] getFineStatistics() {
        String sql = "SELECT " +
                    "COUNT(*) as total_fines, " +
                    "COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_fines, " +
                    "COUNT(CASE WHEN status = 'PAID' THEN 1 END) as paid_fines, " +
                    "COALESCE(SUM(CASE WHEN status = 'PENDING' THEN amount ELSE 0 END), 0) as pending_amount, " +
                    "COALESCE(SUM(CASE WHEN status = 'PAID' THEN amount ELSE 0 END), 0) as collected_amount " +
                    "FROM fines";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return new Object[] {
                    rs.getInt("total_fines"),
                    rs.getInt("pending_fines"),
                    rs.getInt("paid_fines"),
                    rs.getBigDecimal("pending_amount"),
                    rs.getBigDecimal("collected_amount")
                };
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting fine statistics", e);
        }
        return new Object[] {0, 0, 0, BigDecimal.ZERO, BigDecimal.ZERO};
    }

    /**
     * Get fines by date range
     */
    public List<Fine> getFinesByDateRange(LocalDate startDate, LocalDate endDate) {
        List<Fine> fines = new ArrayList<>();
        String sql = "SELECT f.*, u.username, u.first_name, u.last_name, b.title " +
                    "FROM fines f " +
                    "JOIN users u ON f.user_id = u.user_id " +
                    "JOIN borrow_records br ON f.borrow_id = br.borrow_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE f.fine_date BETWEEN ? AND ? ORDER BY f.fine_date DESC";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    fines.add(mapResultSetToFine(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting fines by date range", e);
        }
        return fines;
    }

    /**
     * Map ResultSet to Fine object
     */
    private Fine mapResultSetToFine(ResultSet rs) throws SQLException {
        Fine fine = new Fine();
        fine.setFineId(rs.getInt("fine_id"));
        fine.setBorrowId(rs.getInt("borrow_id"));
        fine.setUserId(rs.getInt("user_id"));
        fine.setAmount(rs.getBigDecimal("amount"));
        fine.setReason(rs.getString("reason"));
        
        Date fineDate = rs.getDate("fine_date");
        if (fineDate != null) {
            fine.setFineDate(fineDate.toLocalDate());
        }
        
        Date paidDate = rs.getDate("paid_date");
        if (paidDate != null) {
            fine.setPaidDate(paidDate.toLocalDate());
        }
        
        fine.setStatus(Fine.FineStatus.valueOf(rs.getString("status")));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            fine.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        // Additional fields from joins
        try {
            fine.setUserName(rs.getString("username"));
            fine.setBookTitle(rs.getString("title"));
        } catch (SQLException e) {
            // These fields might not be present in all queries
        }
        
        return fine;
    }
}
