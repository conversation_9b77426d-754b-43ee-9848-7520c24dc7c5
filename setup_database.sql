-- Library Management System Database Setup Script
-- Run this script to set up the complete database with sample data

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS library_management;
USE library_management;

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS system_statistics;
DROP TABLE IF EXISTS notifications;
DROP TABLE IF EXISTS book_reviews;
DROP TABLE IF EXISTS user_preferences;
DROP TABLE IF EXISTS reading_progress;
DROP TABLE IF EXISTS fines;
DROP TABLE IF EXISTS borrow_records;
DROP TABLE IF EXISTS digital_books;
DROP TABLE IF EXISTS books;
DROP TABLE IF EXISTS users;

-- Users table (both admin and regular users)
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('ADMIN', 'USER') DEFAULT 'USER',
    membership_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Books table (physical books)
CREATE TABLE books (
    book_id INT PRIMARY KEY AUTO_INCREMENT,
    isbn VARCHAR(20) UNIQUE,
    title VARCHAR(255) NOT NULL,
    author VARCHAR(255) NOT NULL,
    genre VARCHAR(100),
    publisher VARCHAR(100),
    publication_year YEAR,
    total_copies INT DEFAULT 1,
    available_copies INT DEFAULT 1,
    description TEXT,
    cover_image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_genre (genre),
    INDEX idx_isbn (isbn)
);

-- Digital books table
CREATE TABLE digital_books (
    digital_book_id INT PRIMARY KEY AUTO_INCREMENT,
    book_id INT,
    file_path VARCHAR(500) NOT NULL,
    file_type ENUM('PDF', 'EPUB') NOT NULL,
    file_size BIGINT,
    total_pages INT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_downloadable BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Borrow records table
CREATE TABLE borrow_records (
    borrow_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    borrow_date DATE NOT NULL,
    due_date DATE NOT NULL,
    return_date DATE NULL,
    status ENUM('BORROWED', 'RETURNED', 'OVERDUE') DEFAULT 'BORROWED',
    renewal_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_book_id (book_id),
    INDEX idx_status (status),
    INDEX idx_due_date (due_date)
);

-- Fines table
CREATE TABLE fines (
    fine_id INT PRIMARY KEY AUTO_INCREMENT,
    borrow_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    reason VARCHAR(255),
    fine_date DATE NOT NULL,
    paid_date DATE NULL,
    status ENUM('PENDING', 'PAID', 'WAIVED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (borrow_id) REFERENCES borrow_records(borrow_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Reading progress table (for digital books)
CREATE TABLE reading_progress (
    progress_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    digital_book_id INT NOT NULL,
    current_page INT DEFAULT 1,
    total_pages_read INT DEFAULT 0,
    reading_time_minutes INT DEFAULT 0,
    last_read_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    bookmarks TEXT, -- JSON array of page numbers
    notes TEXT, -- JSON array of notes with page references
    completion_percentage DECIMAL(5, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (digital_book_id) REFERENCES digital_books(digital_book_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_book (user_id, digital_book_id),
    INDEX idx_user_id (user_id),
    INDEX idx_completion (completion_percentage)
);

-- User preferences for recommendations
CREATE TABLE user_preferences (
    preference_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preferred_genres TEXT, -- JSON array of genres
    preferred_authors TEXT, -- JSON array of authors
    reading_mood VARCHAR(50), -- current mood preference
    notification_preferences TEXT, -- JSON object for notification settings
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preference (user_id)
);

-- Book ratings and reviews
CREATE TABLE book_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    rating INT CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_approved BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_book_review (user_id, book_id),
    INDEX idx_book_rating (book_id, rating)
);

-- Notifications table
CREATE TABLE notifications (
    notification_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('DUE_DATE', 'FINE', 'RECOMMENDATION', 'SYSTEM') NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    INDEX idx_user_unread (user_id, is_read),
    INDEX idx_type (type)
);

-- System statistics table
CREATE TABLE system_statistics (
    stat_id INT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE NOT NULL,
    total_books_borrowed INT DEFAULT 0,
    total_digital_books_accessed INT DEFAULT 0,
    total_fines_collected DECIMAL(10, 2) DEFAULT 0.00,
    active_users_count INT DEFAULT 0,
    popular_genre VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_stat_date (stat_date)
);

-- Insert sample users (password is 'password123' hashed with BCrypt)
INSERT INTO users (username, email, password_hash, first_name, last_name, phone, address, role, membership_date) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'Admin', 'User', '1234567890', '123 Admin St', 'ADMIN', '2023-01-01'),
('john_doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'John', 'Doe', '1234567891', '456 User Ave', 'USER', '2023-02-15'),
('jane_smith', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'Jane', 'Smith', '1234567892', '789 Reader Rd', 'USER', '2023-03-10'),
('bob_wilson', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'Bob', 'Wilson', '1234567893', '321 Book Blvd', 'USER', '2023-04-05');

-- Insert sample books
INSERT INTO books (isbn, title, author, genre, publisher, publication_year, total_copies, available_copies, description) VALUES
('978-0-7432-7356-5', 'The Da Vinci Code', 'Dan Brown', 'Mystery', 'Doubleday', 2003, 3, 2, 'A mystery thriller novel'),
('978-0-06-112008-4', 'To Kill a Mockingbird', 'Harper Lee', 'Fiction', 'J.B. Lippincott & Co.', 1960, 2, 1, 'A classic American novel'),
('978-0-452-28423-4', '1984', 'George Orwell', 'Dystopian Fiction', 'Secker & Warburg', 1949, 4, 3, 'A dystopian social science fiction novel'),
('978-0-7432-4722-4', 'Angels & Demons', 'Dan Brown', 'Mystery', 'Pocket Books', 2000, 2, 2, 'A mystery thriller novel'),
('978-0-316-76948-0', 'The Catcher in the Rye', 'J.D. Salinger', 'Fiction', 'Little, Brown and Company', 1951, 3, 2, 'A coming-of-age story'),
('978-0-06-085052-4', 'Where the Crawdads Sing', 'Delia Owens', 'Fiction', 'G.P. Putnams Sons', 2018, 5, 4, 'A mystery and coming-of-age story'),
('978-0-7432-7357-2', 'The Kite Runner', 'Khaled Hosseini', 'Drama', 'Riverhead Books', 2003, 3, 3, 'A story of friendship and redemption'),
('978-0-553-29698-2', 'A Brief History of Time', 'Stephen Hawking', 'Science', 'Bantam Books', 1988, 2, 1, 'A popular science book'),
('978-0-06-112241-5', 'The Alchemist', 'Paulo Coelho', 'Philosophy', 'HarperCollins', 1988, 4, 3, 'A philosophical novel'),
('978-0-7432-7358-9', 'The Great Gatsby', 'F. Scott Fitzgerald', 'Fiction', 'Charles Scribners Sons', 1925, 3, 2, 'A classic American novel');

-- Insert sample digital books
INSERT INTO digital_books (book_id, file_path, file_type, file_size, total_pages, is_downloadable) VALUES
(1, '/digital_books/da_vinci_code.pdf', 'PDF', 2048000, 454, TRUE),
(3, '/digital_books/1984.pdf', 'PDF', 1536000, 328, TRUE),
(8, '/digital_books/brief_history_time.pdf', 'PDF', 3072000, 256, TRUE),
(9, '/digital_books/alchemist.epub', 'EPUB', 512000, 163, TRUE);

-- Insert sample borrow records
INSERT INTO borrow_records (user_id, book_id, borrow_date, due_date, status) VALUES
(2, 1, '2024-01-15', '2024-02-15', 'BORROWED'),
(3, 2, '2024-01-10', '2024-02-10', 'RETURNED'),
(4, 3, '2024-01-20', '2024-02-20', 'BORROWED'),
(2, 5, '2024-01-25', '2024-02-25', 'BORROWED');

-- Update return date for returned book
UPDATE borrow_records SET return_date = '2024-02-08' WHERE borrow_id = 2;

-- Insert sample fines
INSERT INTO fines (borrow_id, user_id, amount, reason, fine_date, status) VALUES
(1, 2, 5.00, 'Late return - 5 days overdue', '2024-02-20', 'PENDING');

-- Insert sample reading progress
INSERT INTO reading_progress (user_id, digital_book_id, current_page, total_pages_read, reading_time_minutes, completion_percentage, bookmarks) VALUES
(2, 1, 45, 45, 120, 9.91, '[10, 25, 45]'),
(3, 2, 150, 150, 300, 45.73, '[50, 100, 150]'),
(4, 3, 80, 80, 180, 31.25, '[25, 60, 80]');

-- Insert sample user preferences
INSERT INTO user_preferences (user_id, preferred_genres, preferred_authors, reading_mood) VALUES
(2, '["Mystery", "Thriller", "Science Fiction"]', '["Dan Brown", "Stephen King"]', 'adventurous'),
(3, '["Fiction", "Drama", "Romance"]', '["Harper Lee", "Jane Austen"]', 'emotional'),
(4, '["Science", "Philosophy", "Non-fiction"]', '["Stephen Hawking", "Carl Sagan"]', 'intellectual');

-- Insert sample book reviews
INSERT INTO book_reviews (user_id, book_id, rating, review_text) VALUES
(2, 1, 5, 'Excellent thriller! Could not put it down.'),
(3, 2, 5, 'A timeless classic that everyone should read.'),
(4, 8, 4, 'Great introduction to physics concepts.'),
(2, 9, 4, 'Inspiring and thought-provoking.');

-- Insert sample notifications
INSERT INTO notifications (user_id, title, message, type) VALUES
(2, 'Book Due Soon', 'Your book "The Da Vinci Code" is due in 3 days.', 'DUE_DATE'),
(2, 'Outstanding Fine', 'You have an outstanding fine of $5.00 for late return.', 'FINE'),
(3, 'New Recommendation', 'Based on your reading history, you might like "Pride and Prejudice".', 'RECOMMENDATION');

-- Insert sample system statistics
INSERT INTO system_statistics (stat_date, total_books_borrowed, total_digital_books_accessed, total_fines_collected, active_users_count, popular_genre) VALUES
('2024-01-31', 25, 15, 45.00, 3, 'Fiction'),
('2024-02-29', 32, 22, 67.50, 4, 'Mystery');

-- Display setup completion message
SELECT 'Database setup completed successfully!' as Status;
SELECT 'Default admin credentials: username=admin, password=password123' as AdminInfo;
SELECT 'Test user credentials: username=john_doe, password=password123' as UserInfo;
