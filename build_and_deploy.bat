@echo off
echo ========================================
echo Library Management System Build Script
echo ========================================

echo.
echo Step 1: Setting up MySQL Database...
echo Please make sure MySQL is running and accessible with your credentials.
echo.

set /p setup_db="Do you want to set up the database? (y/n): "
if /i "%setup_db%"=="y" (
    echo Setting up database...
    mysql -u root -pHarshalb4u@ < setup_database.sql
    if %errorlevel% neq 0 (
        echo ERROR: Database setup failed!
        pause
        exit /b 1
    )
    echo Database setup completed successfully!
) else (
    echo Skipping database setup...
)

echo.
echo Step 2: Cleaning previous build...
call mvn clean
if %errorlevel% neq 0 (
    echo ERROR: Maven clean failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Compiling the project...
call mvn compile
if %errorlevel% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Running tests...
call mvn test
if %errorlevel% neq 0 (
    echo WARNING: Some tests failed, but continuing...
)

echo.
echo Step 5: Packaging WAR file...
call mvn package
if %errorlevel% neq 0 (
    echo ERROR: Packaging failed!
    pause
    exit /b 1
)

echo.
echo Step 6: Deployment instructions...
echo.
echo WAR file created: target\library-management-system.war
echo.
echo To deploy to Tomcat:
echo 1. Stop Tomcat server if running
echo 2. Copy target\library-management-system.war to TOMCAT_HOME\webapps\
echo 3. Start Tomcat server
echo 4. Access the application at: http://localhost:8080/library-management-system
echo.
echo Default credentials:
echo   Admin: username=admin, password=password123
echo   User:  username=john_doe, password=password123
echo.

set /p auto_deploy="Do you want to copy WAR to Tomcat webapps? (y/n): "
if /i "%auto_deploy%"=="y" (
    set /p tomcat_path="Enter Tomcat installation path (e.g., C:\apache-tomcat-9.0.xx): "
    if exist "%tomcat_path%\webapps" (
        copy target\library-management-system.war "%tomcat_path%\webapps\"
        echo WAR file copied to Tomcat webapps directory.
        echo Please start Tomcat to deploy the application.
    ) else (
        echo ERROR: Invalid Tomcat path or webapps directory not found!
    )
)

echo.
echo ========================================
echo Build process completed!
echo ========================================
pause
