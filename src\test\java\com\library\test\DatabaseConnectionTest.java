package com.library.test;

import com.library.util.DatabaseConnection;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * Test class for database connectivity
 */
public class DatabaseConnectionTest {
    
    private DatabaseConnection dbConnection;
    
    @Before
    public void setUp() {
        dbConnection = DatabaseConnection.getInstance();
        // Initialize with test database credentials
        dbConnection.initialize(
            "com.mysql.cj.jdbc.Driver",
            "******************************************************************************",
            "root",
            "Harshalb4u@"
        );
    }
    
    @Test
    public void testDatabaseConnection() {
        assertTrue("Database connection should be successful", dbConnection.testConnection());
    }
    
    @Test
    public void testUserTableExists() {
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM users");
             ResultSet rs = stmt.executeQuery()) {
            
            assertTrue("Should be able to query users table", rs.next());
            int userCount = rs.getInt(1);
            assertTrue("Should have at least one user", userCount >= 0);
            
        } catch (Exception e) {
            fail("Failed to query users table: " + e.getMessage());
        }
    }
    
    @Test
    public void testBooksTableExists() {
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM books");
             ResultSet rs = stmt.executeQuery()) {
            
            assertTrue("Should be able to query books table", rs.next());
            int bookCount = rs.getInt(1);
            assertTrue("Should have at least zero books", bookCount >= 0);
            
        } catch (Exception e) {
            fail("Failed to query books table: " + e.getMessage());
        }
    }
    
    @Test
    public void testAdminUserExists() {
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT username, role FROM users WHERE username = 'admin'");
             ResultSet rs = stmt.executeQuery()) {
            
            assertTrue("Admin user should exist", rs.next());
            assertEquals("Username should be admin", "admin", rs.getString("username"));
            assertEquals("Role should be ADMIN", "ADMIN", rs.getString("role"));
            
        } catch (Exception e) {
            fail("Failed to find admin user: " + e.getMessage());
        }
    }
}
