package com.library.servlet;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.library.dao.BookDAO;
import com.library.model.Book;
import com.library.util.DatabaseConnection;
import com.library.util.SecurityUtil;

/**
 * Book management servlet
 */
public class BookServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(BookServlet.class.getName());
    private BookDAO bookDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        DatabaseConnection.getInstance().initialize(getServletContext());
        bookDAO = new BookDAO();
        LOGGER.info("BookServlet initialized");
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String pathInfo = request.getPathInfo();

        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleGetAllBooks(request, response);
            } else if (pathInfo.startsWith("/")) {
                String[] pathParts = pathInfo.split("/");
                if (pathParts.length == 2) {
                    try {
                        int bookId = Integer.parseInt(pathParts[1]);
                        handleGetBookById(response, bookId);
                    } catch (NumberFormatException e) {
                        sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                                        "Invalid book ID", "INVALID_BOOK_ID");
                    }
                } else if (pathParts.length == 3 && "genres".equals(pathParts[1])) {
                    handleGetGenres(response);
                } else if (pathParts.length == 3 && "popular".equals(pathParts[1])) {
                    handleGetPopularBooks(request, response);
                } else {
                    sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                                    "Endpoint not found", "ENDPOINT_NOT_FOUND");
                }
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet GET", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // Only admins can create books
        if (!SecurityUtil.isAdmin(request)) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN,
                            "Admin access required", "ADMIN_REQUIRED");
            return;
        }

        try {
            handleCreateBook(request, response);
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet POST", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // Only admins can update books
        if (!SecurityUtil.isAdmin(request)) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN,
                            "Admin access required", "ADMIN_REQUIRED");
            return;
        }

        String pathInfo = request.getPathInfo();
        if (pathInfo == null || pathInfo.equals("/")) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Book ID required", "BOOK_ID_REQUIRED");
            return;
        }

        try {
            String[] pathParts = pathInfo.split("/");
            if (pathParts.length == 2) {
                int bookId = Integer.parseInt(pathParts[1]);
                handleUpdateBook(request, response, bookId);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                                "Invalid request path", "INVALID_PATH");
            }
        } catch (NumberFormatException e) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid book ID", "INVALID_BOOK_ID");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet PUT", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doDelete(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        // Only admins can delete books
        if (!SecurityUtil.isAdmin(request)) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN,
                            "Admin access required", "ADMIN_REQUIRED");
            return;
        }

        String pathInfo = request.getPathInfo();
        if (pathInfo == null || pathInfo.equals("/")) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Book ID required", "BOOK_ID_REQUIRED");
            return;
        }

        try {
            String[] pathParts = pathInfo.split("/");
            if (pathParts.length == 2) {
                int bookId = Integer.parseInt(pathParts[1]);
                handleDeleteBook(response, bookId);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                                "Invalid request path", "INVALID_PATH");
            }
        } catch (NumberFormatException e) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid book ID", "INVALID_BOOK_ID");
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BookServlet DELETE", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    /**
     * Handle getting all books with pagination
     */
    private void handleGetAllBooks(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int page = getIntParameter(request, "page", 1);
        int limit = getIntParameter(request, "limit", 20);
        int offset = (page - 1) * limit;

        List<Book> books = bookDAO.getAllBooks(offset, limit);
        int totalBooks = bookDAO.getTotalBooksCount();

        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("books", convertBooksToJsonArray(books));
        responseData.addProperty("totalBooks", totalBooks);
        responseData.addProperty("currentPage", page);
        responseData.addProperty("totalPages", (int) Math.ceil((double) totalBooks / limit));

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle getting book by ID
     */
    private void handleGetBookById(HttpServletResponse response, int bookId)
            throws IOException {

        Book book = bookDAO.getBookById(bookId);
        if (book == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                            "Book not found", "BOOK_NOT_FOUND");
            return;
        }

        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("book", convertBookToJson(book));

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle getting all genres
     */
    private void handleGetGenres(HttpServletResponse response)
            throws IOException {

        List<String> genres = bookDAO.getAllGenres();

        JsonArray genresArray = new JsonArray();
        for (String genre : genres) {
            genresArray.add(genre);
        }

        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("genres", genresArray);

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle getting popular books
     */
    private void handleGetPopularBooks(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        int limit = getIntParameter(request, "limit", 10);
        List<Book> popularBooks = bookDAO.getMostPopularBooks(limit);

        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("books", convertBooksToJsonArray(popularBooks));

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle creating a new book
     */
    private void handleCreateBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }

        Book book = parseBookFromJson(requestData);
        if (book == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid book data", "INVALID_BOOK_DATA");
            return;
        }

        if (bookDAO.createBook(book)) {
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book created successfully");
            responseData.add("book", convertBookToJson(book));

            sendJsonResponse(response, HttpServletResponse.SC_CREATED, responseData);
            LOGGER.info("Book created: " + book.getTitle());
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Failed to create book", "CREATION_FAILED");
        }
    }

    /**
     * Handle updating a book
     */
    private void handleUpdateBook(HttpServletRequest request, HttpServletResponse response, int bookId)
            throws IOException {

        Book existingBook = bookDAO.getBookById(bookId);
        if (existingBook == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                            "Book not found", "BOOK_NOT_FOUND");
            return;
        }

        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }

        Book updatedBook = parseBookFromJson(requestData);
        if (updatedBook == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid book data", "INVALID_BOOK_DATA");
            return;
        }

        updatedBook.setBookId(bookId);

        if (bookDAO.updateBook(updatedBook)) {
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book updated successfully");
            responseData.add("book", convertBookToJson(updatedBook));

            sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
            LOGGER.info("Book updated: " + updatedBook.getTitle());
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Failed to update book", "UPDATE_FAILED");
        }
    }

    /**
     * Handle deleting a book
     */
    private void handleDeleteBook(HttpServletResponse response, int bookId)
            throws IOException {

        Book book = bookDAO.getBookById(bookId);
        if (book == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                            "Book not found", "BOOK_NOT_FOUND");
            return;
        }

        if (bookDAO.deleteBook(bookId)) {
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book deleted successfully");

            sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
            LOGGER.info("Book deleted: " + book.getTitle());
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Failed to delete book", "DELETION_FAILED");
        }
    }

    // Utility methods
    private JsonObject parseRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }

        try {
            return JsonParser.parseString(sb.toString()).getAsJsonObject();
        } catch (com.google.gson.JsonSyntaxException | IllegalStateException e) {
            LOGGER.log(Level.WARNING, "Failed to parse JSON request body", e);
            return null;
        }
    }

    private Book parseBookFromJson(JsonObject json) {
        try {
            Book book = new Book();

            if (json.has("isbn")) book.setIsbn(json.get("isbn").getAsString());
            if (json.has("title")) book.setTitle(json.get("title").getAsString());
            if (json.has("author")) book.setAuthor(json.get("author").getAsString());
            if (json.has("genre")) book.setGenre(json.get("genre").getAsString());
            if (json.has("publisher")) book.setPublisher(json.get("publisher").getAsString());
            if (json.has("publicationYear")) book.setPublicationYear(json.get("publicationYear").getAsInt());
            if (json.has("totalCopies")) {
                int totalCopies = json.get("totalCopies").getAsInt();
                book.setTotalCopies(totalCopies);
                book.setAvailableCopies(totalCopies); // Default available = total for new books
            }
            if (json.has("description")) book.setDescription(json.get("description").getAsString());
            if (json.has("coverImageUrl")) book.setCoverImageUrl(json.get("coverImageUrl").getAsString());

            // Validate required fields
            if (book.getTitle() == null || book.getAuthor() == null) {
                return null;
            }

            return book;
        } catch (Exception e) {
            return null;
        }
    }

    private JsonObject convertBookToJson(Book book) {
        JsonObject json = new JsonObject();
        json.addProperty("bookId", book.getBookId());
        json.addProperty("isbn", book.getIsbn());
        json.addProperty("title", book.getTitle());
        json.addProperty("author", book.getAuthor());
        json.addProperty("genre", book.getGenre());
        json.addProperty("publisher", book.getPublisher());
        json.addProperty("publicationYear", book.getPublicationYear());
        json.addProperty("totalCopies", book.getTotalCopies());
        json.addProperty("availableCopies", book.getAvailableCopies());
        json.addProperty("description", book.getDescription());
        json.addProperty("coverImageUrl", book.getCoverImageUrl());
        json.addProperty("isAvailable", book.isAvailable());
        return json;
    }

    private JsonArray convertBooksToJsonArray(List<Book> books) {
        JsonArray array = new JsonArray();
        for (Book book : books) {
            array.add(convertBookToJson(book));
        }
        return array;
    }

    private int getIntParameter(HttpServletRequest request, String paramName, int defaultValue) {
        String paramValue = request.getParameter(paramName);
        if (paramValue != null) {
            try {
                return Integer.parseInt(paramValue);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private void sendJsonResponse(HttpServletResponse response, int statusCode, JsonObject data)
            throws IOException {
        response.setStatus(statusCode);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(data.toString());
        }
    }

    private void sendErrorResponse(HttpServletResponse response, int statusCode,
                                 String message, String errorCode) throws IOException {
        JsonObject errorResponse = new JsonObject();
        errorResponse.addProperty("success", false);
        errorResponse.addProperty("message", message);
        errorResponse.addProperty("errorCode", errorCode);

        sendJsonResponse(response, statusCode, errorResponse);
    }
}
