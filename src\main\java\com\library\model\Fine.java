package com.library.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Fine model class representing fines for overdue books
 */
public class Fine {
    private int fineId;
    private int borrowId;
    private int userId;
    private BigDecimal amount;
    private String reason;
    private LocalDate fineDate;
    private LocalDate paidDate;
    private FineStatus status;
    private LocalDateTime createdAt;

    // Additional fields for joined queries
    private String userName;
    private String bookTitle;

    // Constructors
    public Fine() {}

    public Fine(int borrowId, int userId, BigDecimal amount, String reason) {
        this.borrowId = borrowId;
        this.userId = userId;
        this.amount = amount;
        this.reason = reason;
        this.fineDate = LocalDate.now();
        this.status = FineStatus.PENDING;
    }

    // Getters and Setters
    public int getFineId() {
        return fineId;
    }

    public void setFineId(int fineId) {
        this.fineId = fineId;
    }

    public int getBorrowId() {
        return borrowId;
    }

    public void setBorrowId(int borrowId) {
        this.borrowId = borrowId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDate getFineDate() {
        return fineDate;
    }

    public void setFineDate(LocalDate fineDate) {
        this.fineDate = fineDate;
    }

    public LocalDate getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(LocalDate paidDate) {
        this.paidDate = paidDate;
    }

    public FineStatus getStatus() {
        return status;
    }

    public void setStatus(FineStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle;
    }

    // Utility methods
    public boolean isPending() {
        return status == FineStatus.PENDING;
    }

    public boolean isPaid() {
        return status == FineStatus.PAID;
    }

    public boolean isWaived() {
        return status == FineStatus.WAIVED;
    }

    public void markAsPaid() {
        this.status = FineStatus.PAID;
        this.paidDate = LocalDate.now();
    }

    public void waive() {
        this.status = FineStatus.WAIVED;
        this.paidDate = LocalDate.now();
    }

    public long getDaysOutstanding() {
        if (isPaid() || isWaived()) return 0;
        return java.time.temporal.ChronoUnit.DAYS.between(fineDate, LocalDate.now());
    }

    @Override
    public String toString() {
        return "Fine{" +
                "fineId=" + fineId +
                ", borrowId=" + borrowId +
                ", userId=" + userId +
                ", amount=" + amount +
                ", reason='" + reason + '\'' +
                ", fineDate=" + fineDate +
                ", status=" + status +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Fine fine = (Fine) o;
        return fineId == fine.fineId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(fineId);
    }

    // Enum for fine status
    public enum FineStatus {
        PENDING, PAID, WAIVED
    }
}
