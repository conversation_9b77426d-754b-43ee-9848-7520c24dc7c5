import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Simple test to verify database connection
 * Compile and run this to test your database connection
 */
public class TestConnection {
    
    private static final String DB_URL = "******************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "Harshalb4u@";
    
    public static void main(String[] args) {
        System.out.println("Testing MySQL Database Connection...");
        System.out.println("=====================================");
        
        try {
            // Load MySQL JDBC Driver
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✓ MySQL JDBC Driver loaded successfully");
            
            // Test connection
            try (Connection connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD)) {
                System.out.println("✓ Database connection established successfully");
                
                // Test if database exists
                System.out.println("\nTesting database structure...");
                
                // Check if users table exists and has data
                try (PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM users");
                     ResultSet rs = stmt.executeQuery()) {
                    
                    if (rs.next()) {
                        int userCount = rs.getInt(1);
                        System.out.println("✓ Users table exists with " + userCount + " users");
                    }
                } catch (SQLException e) {
                    System.out.println("✗ Users table not found or empty: " + e.getMessage());
                }
                
                // Check if books table exists and has data
                try (PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM books");
                     ResultSet rs = stmt.executeQuery()) {
                    
                    if (rs.next()) {
                        int bookCount = rs.getInt(1);
                        System.out.println("✓ Books table exists with " + bookCount + " books");
                    }
                } catch (SQLException e) {
                    System.out.println("✗ Books table not found or empty: " + e.getMessage());
                }
                
                // Test admin user
                try (PreparedStatement stmt = connection.prepareStatement("SELECT username, role FROM users WHERE username = 'admin'");
                     ResultSet rs = stmt.executeQuery()) {
                    
                    if (rs.next()) {
                        String username = rs.getString("username");
                        String role = rs.getString("role");
                        System.out.println("✓ Admin user found: " + username + " with role: " + role);
                    } else {
                        System.out.println("✗ Admin user not found");
                    }
                } catch (SQLException e) {
                    System.out.println("✗ Error checking admin user: " + e.getMessage());
                }
                
                System.out.println("\n=====================================");
                System.out.println("Database connection test completed!");
                System.out.println("Your backend is ready to connect to the database.");
                
            }
            
        } catch (ClassNotFoundException e) {
            System.out.println("✗ MySQL JDBC Driver not found!");
            System.out.println("Make sure mysql-connector-java is in your classpath");
            e.printStackTrace();
        } catch (SQLException e) {
            System.out.println("✗ Database connection failed!");
            System.out.println("Error: " + e.getMessage());
            System.out.println("\nPossible solutions:");
            System.out.println("1. Make sure MySQL server is running");
            System.out.println("2. Verify username and password are correct");
            System.out.println("3. Check if 'library_management' database exists");
            System.out.println("4. Run the quick_setup.sql script first");
        }
    }
}
