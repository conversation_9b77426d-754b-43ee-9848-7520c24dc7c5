package com.library.servlet;

import com.library.dao.BookDAO;
import com.library.dao.BorrowRecordDAO;
import com.library.dao.FineDAO;
import com.library.model.Book;
import com.library.model.BorrowRecord;
import com.library.model.Fine;
import com.library.util.DatabaseConnection;
import com.library.util.SecurityUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Servlet for handling book borrowing operations
 */
public class BorrowServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(BorrowServlet.class.getName());
    private BorrowRecordDAO borrowRecordDAO;
    private BookDAO bookDAO;
    private FineDAO fineDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        DatabaseConnection.getInstance().initialize(getServletContext());
        borrowRecordDAO = new BorrowRecordDAO();
        bookDAO = new BookDAO();
        fineDAO = new FineDAO();
        LOGGER.info("BorrowServlet initialized");
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String pathInfo = request.getPathInfo();
        
        try {
            if (pathInfo == null || pathInfo.equals("/")) {
                handleBorrowBook(request, response);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                                "Endpoint not found", "ENDPOINT_NOT_FOUND");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BorrowServlet POST", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doPut(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/return".equals(pathInfo)) {
                handleReturnBook(request, response);
            } else if ("/renew".equals(pathInfo)) {
                handleRenewBook(request, response);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                                "Endpoint not found", "ENDPOINT_NOT_FOUND");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BorrowServlet PUT", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        String pathInfo = request.getPathInfo();
        
        try {
            if ("/history".equals(pathInfo)) {
                handleGetBorrowHistory(request, response);
            } else if ("/current".equals(pathInfo)) {
                handleGetCurrentBorrows(request, response);
            } else if ("/overdue".equals(pathInfo)) {
                handleGetOverdueBooks(request, response);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                                "Endpoint not found", "ENDPOINT_NOT_FOUND");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in BorrowServlet GET", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    /**
     * Handle borrowing a book
     */
    private void handleBorrowBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        Integer userId = SecurityUtil.getUserIdFromSession(request);
        if (userId == null) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, 
                            "User not authenticated", "NOT_AUTHENTICATED");
            return;
        }
        
        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }
        
        if (!requestData.has("bookId")) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Book ID is required", "BOOK_ID_REQUIRED");
            return;
        }
        
        int bookId = requestData.get("bookId").getAsInt();
        
        // Check if book exists and is available
        Book book = bookDAO.getBookById(bookId);
        if (book == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                            "Book not found", "BOOK_NOT_FOUND");
            return;
        }
        
        if (!book.isAvailable()) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT, 
                            "Book is not available", "BOOK_NOT_AVAILABLE");
            return;
        }
        
        // Check if user already has this book borrowed
        if (borrowRecordDAO.hasUserBorrowedBook(userId, bookId)) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT, 
                            "You have already borrowed this book", "ALREADY_BORROWED");
            return;
        }
        
        // Check if user has outstanding fines
        BigDecimal outstandingFines = fineDAO.getUserOutstandingFineAmount(userId);
        if (outstandingFines.compareTo(BigDecimal.ZERO) > 0) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, 
                            "Please pay outstanding fines before borrowing", "OUTSTANDING_FINES");
            return;
        }
        
        // Create borrow record
        BorrowRecord borrowRecord = new BorrowRecord();
        borrowRecord.setUserId(userId);
        borrowRecord.setBookId(bookId);
        borrowRecord.setBorrowDate(LocalDate.now());
        borrowRecord.setDueDate(LocalDate.now().plusDays(14)); // 2 weeks loan period
        borrowRecord.setStatus(BorrowRecord.BorrowStatus.BORROWED);
        borrowRecord.setRenewalCount(0);
        
        if (borrowRecordDAO.createBorrowRecord(borrowRecord)) {
            // Update book availability
            book.setAvailableCopies(book.getAvailableCopies() - 1);
            bookDAO.updateAvailability(bookId, book.getAvailableCopies());
            
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book borrowed successfully");
            responseData.add("borrowRecord", convertBorrowRecordToJson(borrowRecord));
            
            sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
            LOGGER.info("Book borrowed successfully: User " + userId + ", Book " + bookId);
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Failed to borrow book", "BORROW_FAILED");
        }
    }

    /**
     * Handle returning a book
     */
    private void handleReturnBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        Integer userId = SecurityUtil.getUserIdFromSession(request);
        if (userId == null) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, 
                            "User not authenticated", "NOT_AUTHENTICATED");
            return;
        }
        
        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }
        
        if (!requestData.has("borrowId")) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Borrow ID is required", "BORROW_ID_REQUIRED");
            return;
        }
        
        int borrowId = requestData.get("borrowId").getAsInt();
        
        // Get borrow record
        BorrowRecord borrowRecord = borrowRecordDAO.getBorrowRecordById(borrowId);
        if (borrowRecord == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                            "Borrow record not found", "BORROW_RECORD_NOT_FOUND");
            return;
        }
        
        // Check if user owns this borrow record (unless admin)
        if (!SecurityUtil.isAdmin(request) && borrowRecord.getUserId() != userId) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, 
                            "Access denied", "ACCESS_DENIED");
            return;
        }
        
        if (borrowRecord.getStatus() != BorrowRecord.BorrowStatus.BORROWED) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT, 
                            "Book is not currently borrowed", "NOT_BORROWED");
            return;
        }
        
        // Check if book is overdue and create fine
        LocalDate today = LocalDate.now();
        if (today.isAfter(borrowRecord.getDueDate())) {
            long overdueDays = java.time.temporal.ChronoUnit.DAYS.between(borrowRecord.getDueDate(), today);
            BigDecimal fineAmount = fineDAO.calculateFineAmount(overdueDays);
            
            Fine fine = new Fine();
            fine.setBorrowId(borrowId);
            fine.setUserId(borrowRecord.getUserId());
            fine.setAmount(fineAmount);
            fine.setReason("Late return - " + overdueDays + " days overdue");
            fine.setFineDate(today);
            fine.setStatus(Fine.FineStatus.PENDING);
            
            fineDAO.createFine(fine);
        }
        
        // Update borrow record
        borrowRecord.setReturnDate(today);
        borrowRecord.setStatus(BorrowRecord.BorrowStatus.RETURNED);
        
        if (borrowRecordDAO.updateBorrowRecord(borrowRecord)) {
            // Update book availability
            Book book = bookDAO.getBookById(borrowRecord.getBookId());
            if (book != null) {
                book.setAvailableCopies(book.getAvailableCopies() + 1);
                bookDAO.updateAvailability(book.getBookId(), book.getAvailableCopies());
            }
            
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book returned successfully");
            responseData.add("borrowRecord", convertBorrowRecordToJson(borrowRecord));
            
            sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
            LOGGER.info("Book returned successfully: Borrow ID " + borrowId);
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Failed to return book", "RETURN_FAILED");
        }
    }

    /**
     * Handle renewing a book
     */
    private void handleRenewBook(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        Integer userId = SecurityUtil.getUserIdFromSession(request);
        if (userId == null) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, 
                            "User not authenticated", "NOT_AUTHENTICATED");
            return;
        }
        
        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }
        
        if (!requestData.has("borrowId")) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST, 
                            "Borrow ID is required", "BORROW_ID_REQUIRED");
            return;
        }
        
        int borrowId = requestData.get("borrowId").getAsInt();
        
        // Get borrow record
        BorrowRecord borrowRecord = borrowRecordDAO.getBorrowRecordById(borrowId);
        if (borrowRecord == null) {
            sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND, 
                            "Borrow record not found", "BORROW_RECORD_NOT_FOUND");
            return;
        }
        
        // Check if user owns this borrow record
        if (borrowRecord.getUserId() != userId) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, 
                            "Access denied", "ACCESS_DENIED");
            return;
        }
        
        if (!borrowRecord.canRenew()) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT, 
                            "Book cannot be renewed", "CANNOT_RENEW");
            return;
        }
        
        // Renew the book (extend by 14 days)
        borrowRecord.setDueDate(borrowRecord.getDueDate().plusDays(14));
        borrowRecord.setRenewalCount(borrowRecord.getRenewalCount() + 1);
        
        if (borrowRecordDAO.updateBorrowRecord(borrowRecord)) {
            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Book renewed successfully");
            responseData.add("borrowRecord", convertBorrowRecordToJson(borrowRecord));
            
            sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
            LOGGER.info("Book renewed successfully: Borrow ID " + borrowId);
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, 
                            "Failed to renew book", "RENEWAL_FAILED");
        }
    }

    /**
     * Handle getting user's borrow history
     */
    private void handleGetBorrowHistory(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        Integer userId = SecurityUtil.getUserIdFromSession(request);
        if (userId == null) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, 
                            "User not authenticated", "NOT_AUTHENTICATED");
            return;
        }
        
        int page = getIntParameter(request, "page", 1);
        int limit = getIntParameter(request, "limit", 20);
        int offset = (page - 1) * limit;
        
        List<BorrowRecord> history = borrowRecordDAO.getUserBorrowHistory(userId, offset, limit);
        
        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("borrowHistory", convertBorrowRecordsToJsonArray(history));
        responseData.addProperty("currentPage", page);
        
        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle getting user's currently borrowed books
     */
    private void handleGetCurrentBorrows(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        Integer userId = SecurityUtil.getUserIdFromSession(request);
        if (userId == null) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, 
                            "User not authenticated", "NOT_AUTHENTICATED");
            return;
        }
        
        List<BorrowRecord> currentBorrows = borrowRecordDAO.getCurrentlyBorrowedBooks(userId);
        
        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("currentBorrows", convertBorrowRecordsToJsonArray(currentBorrows));
        
        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    /**
     * Handle getting overdue books (admin only)
     */
    private void handleGetOverdueBooks(HttpServletRequest request, HttpServletResponse response)
            throws IOException {
        
        if (!SecurityUtil.isAdmin(request)) {
            sendErrorResponse(response, HttpServletResponse.SC_FORBIDDEN, 
                            "Admin access required", "ADMIN_REQUIRED");
            return;
        }
        
        List<BorrowRecord> overdueBooks = borrowRecordDAO.getOverdueBooks();
        
        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.add("overdueBooks", convertBorrowRecordsToJsonArray(overdueBooks));
        
        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    // Utility methods
    private JsonObject parseRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        
        try {
            return JsonParser.parseString(sb.toString()).getAsJsonObject();
        } catch (com.google.gson.JsonSyntaxException | IllegalStateException e) {
            LOGGER.log(Level.WARNING, "Failed to parse JSON request body", e);
            return null;
        }
    }

    private JsonObject convertBorrowRecordToJson(BorrowRecord record) {
        JsonObject json = new JsonObject();
        json.addProperty("borrowId", record.getBorrowId());
        json.addProperty("userId", record.getUserId());
        json.addProperty("bookId", record.getBookId());
        json.addProperty("borrowDate", record.getBorrowDate().toString());
        json.addProperty("dueDate", record.getDueDate().toString());
        if (record.getReturnDate() != null) {
            json.addProperty("returnDate", record.getReturnDate().toString());
        }
        json.addProperty("status", record.getStatus().name());
        json.addProperty("renewalCount", record.getRenewalCount());
        json.addProperty("isOverdue", record.isOverdue());
        json.addProperty("canRenew", record.canRenew());
        
        if (record.getUserName() != null) {
            json.addProperty("userName", record.getUserName());
        }
        if (record.getBookTitle() != null) {
            json.addProperty("bookTitle", record.getBookTitle());
        }
        if (record.getBookAuthor() != null) {
            json.addProperty("bookAuthor", record.getBookAuthor());
        }
        
        return json;
    }

    private JsonArray convertBorrowRecordsToJsonArray(List<BorrowRecord> records) {
        JsonArray array = new JsonArray();
        for (BorrowRecord record : records) {
            array.add(convertBorrowRecordToJson(record));
        }
        return array;
    }

    private int getIntParameter(HttpServletRequest request, String paramName, int defaultValue) {
        String paramValue = request.getParameter(paramName);
        if (paramValue != null) {
            try {
                return Integer.parseInt(paramValue);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private void sendJsonResponse(HttpServletResponse response, int statusCode, JsonObject data) 
            throws IOException {
        response.setStatus(statusCode);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(data.toString());
        }
    }

    private void sendErrorResponse(HttpServletResponse response, int statusCode, 
                                 String message, String errorCode) throws IOException {
        JsonObject errorResponse = new JsonObject();
        errorResponse.addProperty("success", false);
        errorResponse.addProperty("message", message);
        errorResponse.addProperty("errorCode", errorCode);
        
        sendJsonResponse(response, statusCode, errorResponse);
    }
}
