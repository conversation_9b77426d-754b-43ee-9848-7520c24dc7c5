<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Library Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .api-section h3 {
            color: #555;
            margin-top: 0;
        }
        .endpoint {
            font-family: monospace;
            background-color: #e8e8e8;
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            display: block;
        }
        .method {
            font-weight: bold;
            color: #007bff;
        }
        .description {
            color: #666;
            font-style: italic;
            margin-top: 5px;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 Library Management System</h1>
        
        <div class="status success">
            <strong>✅ System Status:</strong> Backend services are running
        </div>

        <div class="api-section">
            <h3>🔐 Authentication Endpoints</h3>
            <div class="endpoint">
                <span class="method">POST</span> /auth/login
                <div class="description">User login with username/password</div>
            </div>
            <div class="endpoint">
                <span class="method">POST</span> /auth/register
                <div class="description">User registration</div>
            </div>
            <div class="endpoint">
                <span class="method">POST</span> /auth/logout
                <div class="description">User logout</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /auth/status
                <div class="description">Check authentication status</div>
            </div>
        </div>

        <div class="api-section">
            <h3>📖 Book Management Endpoints</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/books
                <div class="description">Get all books with pagination</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/books/{id}
                <div class="description">Get book by ID</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/books/genres
                <div class="description">Get all available genres</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/books/popular
                <div class="description">Get most popular books</div>
            </div>
            <div class="endpoint">
                <span class="method">POST</span> /api/books <em>(Admin only)</em>
                <div class="description">Create new book</div>
            </div>
            <div class="endpoint">
                <span class="method">PUT</span> /api/books/{id} <em>(Admin only)</em>
                <div class="description">Update book information</div>
            </div>
            <div class="endpoint">
                <span class="method">DELETE</span> /api/books/{id} <em>(Admin only)</em>
                <div class="description">Delete book</div>
            </div>
        </div>

        <div class="api-section">
            <h3>🔍 Search & Recommendations</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/search
                <div class="description">Search books by title, author, genre, or keyword</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/recommendations
                <div class="description">Get personalized book recommendations</div>
            </div>
        </div>

        <div class="api-section">
            <h3>📚 Borrowing System</h3>
            <div class="endpoint">
                <span class="method">POST</span> /api/borrow
                <div class="description">Borrow a book</div>
            </div>
            <div class="endpoint">
                <span class="method">PUT</span> /api/borrow/return
                <div class="description">Return a borrowed book</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/borrow/history
                <div class="description">Get user's borrowing history</div>
            </div>
        </div>

        <div class="api-section">
            <h3>💻 Digital Library</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/digital/books
                <div class="description">Get available digital books</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/digital/read/{id}
                <div class="description">Read digital book online</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/digital/download/{id}
                <div class="description">Download digital book</div>
            </div>
            <div class="endpoint">
                <span class="method">POST</span> /api/digital/upload <em>(Admin only)</em>
                <div class="description">Upload digital book</div>
            </div>
        </div>

        <div class="api-section">
            <h3>💰 Fine Management</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/fines
                <div class="description">Get user's fines</div>
            </div>
            <div class="endpoint">
                <span class="method">POST</span> /api/fines/pay
                <div class="description">Pay fine</div>
            </div>
            <div class="endpoint">
                <span class="method">PUT</span> /api/fines/waive <em>(Admin only)</em>
                <div class="description">Waive fine</div>
            </div>
        </div>

        <div class="api-section">
            <h3>📊 Reports & Analytics</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/reports/overview <em>(Admin only)</em>
                <div class="description">System overview statistics</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/reports/popular-books <em>(Admin only)</em>
                <div class="description">Most borrowed books report</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/reports/fines <em>(Admin only)</em>
                <div class="description">Fines collection report</div>
            </div>
        </div>

        <div class="api-section">
            <h3>👥 User Management</h3>
            <div class="endpoint">
                <span class="method">GET</span> /api/users <em>(Admin only)</em>
                <div class="description">Get all users</div>
            </div>
            <div class="endpoint">
                <span class="method">GET</span> /api/users/profile
                <div class="description">Get current user profile</div>
            </div>
            <div class="endpoint">
                <span class="method">PUT</span> /api/users/profile
                <div class="description">Update user profile</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p><strong>Default Admin Credentials:</strong></p>
            <p>Username: <code>admin</code> | Password: <code>password123</code></p>
            <p><strong>Test User Credentials:</strong></p>
            <p>Username: <code>john_doe</code> | Password: <code>password123</code></p>
        </div>
    </div>
</body>
</html>
