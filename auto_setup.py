#!/usr/bin/env python3
"""
Automated Database Setup and Backend Connection Script
This script will:
1. Connect to your MySQL database
2. Create the library_management database
3. Create all required tables
4. Insert sample data
5. Test the connection from Java backend
"""

import mysql.connector
import subprocess
import sys
import os
from pathlib import Path

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'Harshalb4u@',
    'charset': 'utf8mb4',
    'collation': 'utf8mb4_unicode_ci'
}

def print_step(step_num, description):
    print(f"\n{'='*50}")
    print(f"Step {step_num}: {description}")
    print('='*50)

def connect_to_mysql():
    """Connect to MySQL server"""
    try:
        print("Connecting to MySQL server...")
        connection = mysql.connector.connect(**DB_CONFIG)
        print("✓ Successfully connected to MySQL server")
        return connection
    except mysql.connector.Error as err:
        print(f"✗ Error connecting to MySQL: {err}")
        print("\nPossible solutions:")
        print("1. Make sure MySQL server is running")
        print("2. Verify the password 'Harshalb4u@' is correct")
        print("3. Check if MySQL is installed and accessible")
        return None

def create_database(connection):
    """Create the library_management database"""
    try:
        cursor = connection.cursor()
        
        # Create database
        cursor.execute("CREATE DATABASE IF NOT EXISTS library_management")
        cursor.execute("USE library_management")
        print("✓ Database 'library_management' created/verified")
        
        return cursor
    except mysql.connector.Error as err:
        print(f"✗ Error creating database: {err}")
        return None

def create_tables(cursor):
    """Create all required tables"""
    try:
        print("Creating tables...")
        
        # Users table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            user_id INT PRIMARY KEY AUTO_INCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20),
            address TEXT,
            role ENUM('ADMIN', 'USER') DEFAULT 'USER',
            membership_date DATE NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
        """)
        
        # Books table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS books (
            book_id INT PRIMARY KEY AUTO_INCREMENT,
            isbn VARCHAR(20) UNIQUE,
            title VARCHAR(255) NOT NULL,
            author VARCHAR(255) NOT NULL,
            genre VARCHAR(100),
            publisher VARCHAR(100),
            publication_year YEAR,
            total_copies INT DEFAULT 1,
            available_copies INT DEFAULT 1,
            description TEXT,
            cover_image_url VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_title (title),
            INDEX idx_author (author),
            INDEX idx_genre (genre)
        )
        """)
        
        # Borrow records table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS borrow_records (
            borrow_id INT PRIMARY KEY AUTO_INCREMENT,
            user_id INT NOT NULL,
            book_id INT NOT NULL,
            borrow_date DATE NOT NULL,
            due_date DATE NOT NULL,
            return_date DATE NULL,
            status ENUM('BORROWED', 'RETURNED', 'OVERDUE') DEFAULT 'BORROWED',
            renewal_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
        )
        """)
        
        # Fines table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS fines (
            fine_id INT PRIMARY KEY AUTO_INCREMENT,
            borrow_id INT NOT NULL,
            user_id INT NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            reason VARCHAR(255),
            fine_date DATE NOT NULL,
            paid_date DATE NULL,
            status ENUM('PENDING', 'PAID', 'WAIVED') DEFAULT 'PENDING',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (borrow_id) REFERENCES borrow_records(borrow_id) ON DELETE CASCADE,
            FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
        )
        """)
        
        print("✓ All tables created successfully")
        return True
        
    except mysql.connector.Error as err:
        print(f"✗ Error creating tables: {err}")
        return False

def insert_sample_data(cursor):
    """Insert sample users and books"""
    try:
        print("Inserting sample data...")
        
        # Check if data already exists
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        
        if user_count > 0:
            print("✓ Sample data already exists, skipping insertion")
            return True
        
        # Insert admin user (password: password123)
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, first_name, last_name, role, membership_date) VALUES
        ('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'Admin', 'User', 'ADMIN', '2024-01-01')
        """)
        
        # Insert test user (password: password123)
        cursor.execute("""
        INSERT INTO users (username, email, password_hash, first_name, last_name, role, membership_date) VALUES
        ('john_doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'John', 'Doe', 'USER', '2024-01-15')
        """)
        
        # Insert sample books
        books_data = [
            ('978-0-7432-7356-5', 'The Da Vinci Code', 'Dan Brown', 'Mystery', 'Doubleday', 2003, 3, 3, 'A mystery thriller novel'),
            ('978-0-06-112008-4', 'To Kill a Mockingbird', 'Harper Lee', 'Fiction', 'J.B. Lippincott & Co.', 1960, 2, 2, 'A classic American novel'),
            ('978-0-452-28423-4', '1984', 'George Orwell', 'Dystopian Fiction', 'Secker & Warburg', 1949, 4, 4, 'A dystopian social science fiction novel'),
            ('978-0-316-76948-0', 'The Catcher in the Rye', 'J.D. Salinger', 'Fiction', 'Little, Brown and Company', 1951, 3, 3, 'A coming-of-age story'),
            ('978-0-06-085052-4', 'Where the Crawdads Sing', 'Delia Owens', 'Fiction', 'G.P. Putnams Sons', 2018, 5, 5, 'A mystery and coming-of-age story')
        ]
        
        for book in books_data:
            cursor.execute("""
            INSERT INTO books (isbn, title, author, genre, publisher, publication_year, total_copies, available_copies, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, book)
        
        print("✓ Sample data inserted successfully")
        return True
        
    except mysql.connector.Error as err:
        print(f"✗ Error inserting sample data: {err}")
        return False

def verify_setup(cursor):
    """Verify the database setup"""
    try:
        print("Verifying database setup...")
        
        # Check users
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"✓ Users table: {user_count} users")
        
        # Check books
        cursor.execute("SELECT COUNT(*) FROM books")
        book_count = cursor.fetchone()[0]
        print(f"✓ Books table: {book_count} books")
        
        # Check admin user
        cursor.execute("SELECT username, role FROM users WHERE username = 'admin'")
        admin = cursor.fetchone()
        if admin:
            print(f"✓ Admin user: {admin[0]} with role {admin[1]}")
        
        return True
        
    except mysql.connector.Error as err:
        print(f"✗ Error verifying setup: {err}")
        return False

def test_java_connection():
    """Test Java backend connection"""
    try:
        print("Testing Java backend connection...")
        
        # Check if Maven is available
        result = subprocess.run(['mvn', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ Maven is available")
            
            # Run Maven test
            print("Running Maven tests...")
            result = subprocess.run(['mvn', 'test', '-Dtest=DatabaseConnectionTest'], 
                                  capture_output=True, text=True, cwd='.')
            
            if result.returncode == 0:
                print("✓ Java backend connection test passed")
            else:
                print("⚠ Java backend test had issues, but database is ready")
                print("You can manually test with: mvn test")
        else:
            print("⚠ Maven not found, skipping Java test")
            print("Database is ready for your Java backend")
        
        return True
        
    except Exception as e:
        print(f"⚠ Could not test Java connection: {e}")
        print("Database is ready, you can test manually")
        return True

def main():
    print("🚀 Library Management System - Automated Database Setup")
    print("This script will set up your database and connect it to the backend")
    
    # Step 1: Connect to MySQL
    print_step(1, "Connecting to MySQL Server")
    connection = connect_to_mysql()
    if not connection:
        sys.exit(1)
    
    try:
        # Step 2: Create database
        print_step(2, "Creating Database")
        cursor = create_database(connection)
        if not cursor:
            sys.exit(1)
        
        # Step 3: Create tables
        print_step(3, "Creating Tables")
        if not create_tables(cursor):
            sys.exit(1)
        
        # Step 4: Insert sample data
        print_step(4, "Inserting Sample Data")
        if not insert_sample_data(cursor):
            sys.exit(1)
        
        # Commit changes
        connection.commit()
        
        # Step 5: Verify setup
        print_step(5, "Verifying Setup")
        if not verify_setup(cursor):
            sys.exit(1)
        
        # Step 6: Test Java connection
        print_step(6, "Testing Backend Connection")
        test_java_connection()
        
        # Success message
        print(f"\n{'='*60}")
        print("🎉 DATABASE SETUP COMPLETED SUCCESSFULLY!")
        print('='*60)
        print("\n📋 Setup Summary:")
        print("✓ Database 'library_management' created")
        print("✓ All tables created with proper relationships")
        print("✓ Sample data inserted")
        print("✓ Backend connection configured")
        
        print("\n🔐 Default Credentials:")
        print("Admin: username=admin, password=password123")
        print("User:  username=john_doe, password=password123")
        
        print("\n🚀 Next Steps:")
        print("1. Build your application: mvn clean package")
        print("2. Deploy to Tomcat")
        print("3. Access at: http://localhost:8080/library-management-system")
        
    except Exception as e:
        print(f"\n✗ Unexpected error: {e}")
        sys.exit(1)
    
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("\n✓ Database connection closed")

if __name__ == "__main__":
    main()
