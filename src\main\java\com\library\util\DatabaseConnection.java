package com.library.util;

import javax.servlet.ServletContext;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Database connection utility class using singleton pattern
 */
public class DatabaseConnection {
    private static final Logger LOGGER = Logger.getLogger(DatabaseConnection.class.getName());
    
    private static DatabaseConnection instance;
    private String url;
    private String username;
    private String password;
    private String driver;

    // Private constructor to prevent instantiation
    private DatabaseConnection() {}

    /**
     * Get singleton instance of DatabaseConnection
     */
    public static synchronized DatabaseConnection getInstance() {
        if (instance == null) {
            instance = new DatabaseConnection();
        }
        return instance;
    }

    /**
     * Initialize database connection parameters from servlet context
     */
    public void initialize(ServletContext context) {
        this.driver = context.getInitParameter("db.driver");
        this.url = context.getInitParameter("db.url");
        this.username = context.getInitParameter("db.username");
        this.password = context.getInitParameter("db.password");

        try {
            Class.forName(driver);
            LOGGER.info("Database driver loaded successfully: " + driver);
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "Failed to load database driver: " + driver, e);
            throw new RuntimeException("Database driver not found", e);
        }
    }

    /**
     * Initialize with custom parameters (for testing)
     */
    public void initialize(String driver, String url, String username, String password) {
        this.driver = driver;
        this.url = url;
        this.username = username;
        this.password = password;

        try {
            Class.forName(driver);
            LOGGER.info("Database driver loaded successfully: " + driver);
        } catch (ClassNotFoundException e) {
            LOGGER.log(Level.SEVERE, "Failed to load database driver: " + driver, e);
            throw new RuntimeException("Database driver not found", e);
        }
    }

    /**
     * Get a database connection
     */
    public Connection getConnection() throws SQLException {
        if (url == null || username == null || password == null) {
            throw new SQLException("Database connection not initialized");
        }

        try {
            Connection connection = DriverManager.getConnection(url, username, password);
            connection.setAutoCommit(true); // Default to auto-commit
            return connection;
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Failed to create database connection", e);
            throw e;
        }
    }

    /**
     * Get a database connection with transaction control
     */
    public Connection getConnectionWithTransaction() throws SQLException {
        Connection connection = getConnection();
        connection.setAutoCommit(false);
        return connection;
    }

    /**
     * Close database connection safely
     */
    public static void closeConnection(Connection connection) {
        if (connection != null) {
            try {
                connection.close();
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Failed to close database connection", e);
            }
        }
    }

    /**
     * Commit transaction and close connection
     */
    public static void commitAndClose(Connection connection) {
        if (connection != null) {
            try {
                connection.commit();
                connection.close();
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Failed to commit transaction or close connection", e);
            }
        }
    }

    /**
     * Rollback transaction and close connection
     */
    public static void rollbackAndClose(Connection connection) {
        if (connection != null) {
            try {
                connection.rollback();
                connection.close();
            } catch (SQLException e) {
                LOGGER.log(Level.WARNING, "Failed to rollback transaction or close connection", e);
            }
        }
    }

    /**
     * Test database connectivity
     */
    public boolean testConnection() {
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            LOGGER.log(Level.WARNING, "Database connection test failed", e);
            return false;
        }
    }

    // Getters for connection parameters (for debugging)
    public String getUrl() {
        return url;
    }

    public String getUsername() {
        return username;
    }

    public String getDriver() {
        return driver;
    }
}
