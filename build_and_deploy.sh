#!/bin/bash

echo "========================================"
echo "Library Management System Build Script"
echo "========================================"

echo ""
echo "Step 1: Setting up MySQL Database..."
echo "Please make sure MySQL is running and accessible with your credentials."
echo ""

read -p "Do you want to set up the database? (y/n): " setup_db
if [[ $setup_db == "y" || $setup_db == "Y" ]]; then
    echo "Setting up database..."
    mysql -u root -pHarshalb4u@ < setup_database.sql
    if [ $? -ne 0 ]; then
        echo "ERROR: Database setup failed!"
        exit 1
    fi
    echo "Database setup completed successfully!"
else
    echo "Skipping database setup..."
fi

echo ""
echo "Step 2: Cleaning previous build..."
mvn clean
if [ $? -ne 0 ]; then
    echo "ERROR: Maven clean failed!"
    exit 1
fi

echo ""
echo "Step 3: Compiling the project..."
mvn compile
if [ $? -ne 0 ]; then
    echo "ERROR: Compilation failed!"
    exit 1
fi

echo ""
echo "Step 4: Running tests..."
mvn test
if [ $? -ne 0 ]; then
    echo "WARNING: Some tests failed, but continuing..."
fi

echo ""
echo "Step 5: Packaging WAR file..."
mvn package
if [ $? -ne 0 ]; then
    echo "ERROR: Packaging failed!"
    exit 1
fi

echo ""
echo "Step 6: Deployment instructions..."
echo ""
echo "WAR file created: target/library-management-system.war"
echo ""
echo "To deploy to Tomcat:"
echo "1. Stop Tomcat server if running"
echo "2. Copy target/library-management-system.war to TOMCAT_HOME/webapps/"
echo "3. Start Tomcat server"
echo "4. Access the application at: http://localhost:8080/library-management-system"
echo ""
echo "Default credentials:"
echo "  Admin: username=admin, password=password123"
echo "  User:  username=john_doe, password=password123"
echo ""

read -p "Do you want to copy WAR to Tomcat webapps? (y/n): " auto_deploy
if [[ $auto_deploy == "y" || $auto_deploy == "Y" ]]; then
    read -p "Enter Tomcat installation path (e.g., /opt/tomcat): " tomcat_path
    if [ -d "$tomcat_path/webapps" ]; then
        cp target/library-management-system.war "$tomcat_path/webapps/"
        echo "WAR file copied to Tomcat webapps directory."
        echo "Please start Tomcat to deploy the application."
    else
        echo "ERROR: Invalid Tomcat path or webapps directory not found!"
    fi
fi

echo ""
echo "========================================"
echo "Build process completed!"
echo "========================================"
