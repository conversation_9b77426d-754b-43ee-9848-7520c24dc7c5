-- Quick Database Setup for Library Management System
-- Copy and paste this into MySQL command line or MySQL Workbench

-- Create database
CREATE DATABASE IF NOT EXISTS library_management;
USE library_management;

-- Create users table
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    role ENUM('ADMIN', 'USER') DEFAULT 'USER',
    membership_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create books table
CREATE TABLE books (
    book_id INT PRIMARY KEY AUTO_INCREMENT,
    isbn VARCHAR(20) UNIQUE,
    title VARCHAR(255) NOT NULL,
    author <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    genre VARCHAR(100),
    publisher VARCHAR(100),
    publication_year YEAR,
    total_copies INT DEFAULT 1,
    available_copies INT DEFAULT 1,
    description TEXT,
    cover_image_url VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_title (title),
    INDEX idx_author (author),
    INDEX idx_genre (genre)
);

-- Create borrow_records table
CREATE TABLE borrow_records (
    borrow_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    book_id INT NOT NULL,
    borrow_date DATE NOT NULL,
    due_date DATE NOT NULL,
    return_date DATE NULL,
    status ENUM('BORROWED', 'RETURNED', 'OVERDUE') DEFAULT 'BORROWED',
    renewal_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(book_id) ON DELETE CASCADE
);

-- Create fines table
CREATE TABLE fines (
    fine_id INT PRIMARY KEY AUTO_INCREMENT,
    borrow_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    reason VARCHAR(255),
    fine_date DATE NOT NULL,
    paid_date DATE NULL,
    status ENUM('PENDING', 'PAID', 'WAIVED') DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (borrow_id) REFERENCES borrow_records(borrow_id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Insert admin user (password: password123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role, membership_date) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'Admin', 'User', 'ADMIN', '2024-01-01');

-- Insert test user (password: password123)
INSERT INTO users (username, email, password_hash, first_name, last_name, role, membership_date) VALUES
('john_doe', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBLpLk.bQ.H6jG', 'John', 'Doe', 'USER', '2024-01-15');

-- Insert sample books
INSERT INTO books (isbn, title, author, genre, publisher, publication_year, total_copies, available_copies, description) VALUES
('978-0-7432-7356-5', 'The Da Vinci Code', 'Dan Brown', 'Mystery', 'Doubleday', 2003, 3, 3, 'A mystery thriller novel'),
('978-0-06-112008-4', 'To Kill a Mockingbird', 'Harper Lee', 'Fiction', 'J.B. Lippincott & Co.', 1960, 2, 2, 'A classic American novel'),
('978-0-452-28423-4', '1984', 'George Orwell', 'Dystopian Fiction', 'Secker & Warburg', 1949, 4, 4, 'A dystopian social science fiction novel'),
('978-0-316-76948-0', 'The Catcher in the Rye', 'J.D. Salinger', 'Fiction', 'Little, Brown and Company', 1951, 3, 3, 'A coming-of-age story'),
('978-0-06-085052-4', 'Where the Crawdads Sing', 'Delia Owens', 'Fiction', 'G.P. Putnams Sons', 2018, 5, 5, 'A mystery and coming-of-age story');

-- Verify setup
SELECT 'Database setup completed!' as Status;
SELECT COUNT(*) as UserCount FROM users;
SELECT COUNT(*) as BookCount FROM books;
SELECT 'Admin user created with username: admin, password: password123' as AdminInfo;
SELECT 'Test user created with username: john_doe, password: password123' as UserInfo;
