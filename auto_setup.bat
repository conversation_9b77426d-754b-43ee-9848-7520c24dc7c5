@echo off
echo ========================================
echo Library Management System Auto Setup
echo ========================================

echo.
echo This script will automatically:
echo 1. Connect to your MySQL database
echo 2. Create the library_management database
echo 3. Create all required tables
echo 4. Insert sample data
echo 5. Test backend connection
echo.

echo Database Configuration:
echo Host: localhost
echo Username: root
echo Password: Harshalb4u@
echo.

set /p confirm="Continue with automatic setup? (y/n): "
if /i "%confirm%" neq "y" (
    echo Setup cancelled.
    pause
    exit /b 0
)

echo.
echo Starting automatic setup...

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Python is not installed or not in PATH
        echo Please install Python 3.x and try again
        echo Download from: https://www.python.org/downloads/
        pause
        exit /b 1
    )
    set PYTHON_CMD=python3
) else (
    set PYTHON_CMD=python
)

echo Using Python: %PYTHON_CMD%

REM Install required Python packages
echo.
echo Installing required Python packages...
%PYTHON_CMD% -m pip install mysql-connector-python

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install mysql-connector-python
    echo Please run: pip install mysql-connector-python
    pause
    exit /b 1
)

REM Run the setup script
echo.
echo Running database setup...
%PYTHON_CMD% auto_setup.py

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Database setup failed
    echo Please check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup completed successfully!
echo ========================================
echo.
echo Your database is now ready!
echo You can now build and deploy your application.
echo.
pause
