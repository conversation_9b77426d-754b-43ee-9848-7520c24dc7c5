package com.library.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ReadingProgress model class for tracking digital book reading progress
 */
public class ReadingProgress {
    private int progressId;
    private int userId;
    private int digitalBookId;
    private int currentPage;
    private int totalPagesRead;
    private int readingTimeMinutes;
    private LocalDateTime lastReadDate;
    private List<Integer> bookmarks;
    private List<Note> notes;
    private BigDecimal completionPercentage;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Additional fields for joined queries
    private String userName;
    private String bookTitle;
    private String bookAuthor;

    // Constructors
    public ReadingProgress() {}

    public ReadingProgress(int userId, int digitalBookId) {
        this.userId = userId;
        this.digitalBookId = digitalBookId;
        this.currentPage = 1;
        this.totalPagesRead = 0;
        this.readingTimeMinutes = 0;
        this.completionPercentage = BigDecimal.ZERO;
        this.lastReadDate = LocalDateTime.now();
    }

    // Getters and Setters
    public int getProgressId() {
        return progressId;
    }

    public void setProgressId(int progressId) {
        this.progressId = progressId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getDigitalBookId() {
        return digitalBookId;
    }

    public void setDigitalBookId(int digitalBookId) {
        this.digitalBookId = digitalBookId;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getTotalPagesRead() {
        return totalPagesRead;
    }

    public void setTotalPagesRead(int totalPagesRead) {
        this.totalPagesRead = totalPagesRead;
    }

    public int getReadingTimeMinutes() {
        return readingTimeMinutes;
    }

    public void setReadingTimeMinutes(int readingTimeMinutes) {
        this.readingTimeMinutes = readingTimeMinutes;
    }

    public LocalDateTime getLastReadDate() {
        return lastReadDate;
    }

    public void setLastReadDate(LocalDateTime lastReadDate) {
        this.lastReadDate = lastReadDate;
    }

    public List<Integer> getBookmarks() {
        return bookmarks;
    }

    public void setBookmarks(List<Integer> bookmarks) {
        this.bookmarks = bookmarks;
    }

    public List<Note> getNotes() {
        return notes;
    }

    public void setNotes(List<Note> notes) {
        this.notes = notes;
    }

    public BigDecimal getCompletionPercentage() {
        return completionPercentage;
    }

    public void setCompletionPercentage(BigDecimal completionPercentage) {
        this.completionPercentage = completionPercentage;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle;
    }

    public String getBookAuthor() {
        return bookAuthor;
    }

    public void setBookAuthor(String bookAuthor) {
        this.bookAuthor = bookAuthor;
    }

    // Utility methods
    public void updateProgress(int newPage, int totalPages) {
        this.currentPage = newPage;
        this.lastReadDate = LocalDateTime.now();
        
        if (newPage > this.totalPagesRead) {
            this.totalPagesRead = newPage;
        }
        
        if (totalPages > 0) {
            this.completionPercentage = BigDecimal.valueOf((double) newPage / totalPages * 100)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }

    public void addReadingTime(int minutes) {
        this.readingTimeMinutes += minutes;
        this.lastReadDate = LocalDateTime.now();
    }

    public void addBookmark(int pageNumber) {
        if (bookmarks != null && !bookmarks.contains(pageNumber)) {
            bookmarks.add(pageNumber);
        }
    }

    public void removeBookmark(int pageNumber) {
        if (bookmarks != null) {
            bookmarks.remove(Integer.valueOf(pageNumber));
        }
    }

    public boolean isCompleted() {
        return completionPercentage != null && 
               completionPercentage.compareTo(BigDecimal.valueOf(100)) >= 0;
    }

    public String getFormattedReadingTime() {
        if (readingTimeMinutes < 60) {
            return readingTimeMinutes + " minutes";
        } else {
            int hours = readingTimeMinutes / 60;
            int minutes = readingTimeMinutes % 60;
            return hours + "h " + minutes + "m";
        }
    }

    @Override
    public String toString() {
        return "ReadingProgress{" +
                "progressId=" + progressId +
                ", userId=" + userId +
                ", digitalBookId=" + digitalBookId +
                ", currentPage=" + currentPage +
                ", completionPercentage=" + completionPercentage +
                ", readingTimeMinutes=" + readingTimeMinutes +
                '}';
    }

    // Inner class for notes
    public static class Note {
        private int pageNumber;
        private String noteText;
        private LocalDateTime createdAt;

        public Note() {}

        public Note(int pageNumber, String noteText) {
            this.pageNumber = pageNumber;
            this.noteText = noteText;
            this.createdAt = LocalDateTime.now();
        }

        // Getters and setters for Note
        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
        public String getNoteText() { return noteText; }
        public void setNoteText(String noteText) { this.noteText = noteText; }
        public LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    }
}
