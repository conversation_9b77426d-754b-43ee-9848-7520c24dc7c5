package com.library.servlet;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.util.logging.Level;
import java.util.logging.Logger;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.library.dao.UserDAO;
import com.library.model.User;
import com.library.util.DatabaseConnection;
import com.library.util.SecurityUtil;

/**
 * Authentication servlet handling login, registration, and logout
 */
public class AuthServlet extends HttpServlet {
    private static final Logger LOGGER = Logger.getLogger(AuthServlet.class.getName());
    private UserDAO userDAO;

    @Override
    public void init() throws ServletException {
        super.init();
        // Initialize database connection
        DatabaseConnection.getInstance().initialize(getServletContext());
        userDAO = new UserDAO();
        LOGGER.info("AuthServlet initialized");
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String pathInfo = request.getPathInfo();

        try {
            switch (pathInfo) {
                case "/login":
                    handleLogin(request, response);
                    break;
                case "/register":
                    handleRegister(request, response);
                    break;
                case "/logout":
                    handleLogout(request, response);
                    break;
                default:
                    sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                                    "Endpoint not found", "ENDPOINT_NOT_FOUND");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in AuthServlet", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        String pathInfo = request.getPathInfo();

        try {
            if ("/status".equals(pathInfo)) {
                handleAuthStatus(request, response);
            } else {
                sendErrorResponse(response, HttpServletResponse.SC_NOT_FOUND,
                                "Endpoint not found", "ENDPOINT_NOT_FOUND");
            }
        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "Error in AuthServlet GET", e);
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Internal server error", "INTERNAL_ERROR");
        }
    }

    /**
     * Handle user login
     */
    private void handleLogin(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }

        String username = getStringFromJson(requestData, "username");
        String password = getStringFromJson(requestData, "password");

        // Validate input
        if (username == null || username.trim().isEmpty() ||
            password == null || password.trim().isEmpty()) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Username and password are required", "MISSING_CREDENTIALS");
            return;
        }

        // Authenticate user
        User user = userDAO.getUserByUsername(username.trim());
        if (user == null || !user.isActive()) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED,
                            "Invalid username or password", "INVALID_CREDENTIALS");
            return;
        }

        if (!SecurityUtil.verifyPassword(password, user.getPasswordHash())) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED,
                            "Invalid username or password", "INVALID_CREDENTIALS");
            return;
        }

        // Create session
        SecurityUtil.createUserSession(request, user.getUserId(),
                                     user.getUsername(), user.getRole().name());

        // Send success response
        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.addProperty("message", "Login successful");

        JsonObject userData = new JsonObject();
        userData.addProperty("userId", user.getUserId());
        userData.addProperty("username", user.getUsername());
        userData.addProperty("email", user.getEmail());
        userData.addProperty("firstName", user.getFirstName());
        userData.addProperty("lastName", user.getLastName());
        userData.addProperty("role", user.getRole().name());
        userData.addProperty("csrfToken", SecurityUtil.getCSRFToken(request));

        responseData.add("user", userData);

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
        LOGGER.info("User logged in successfully: " + username);
    }

    /**
     * Handle user registration
     */
    private void handleRegister(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        JsonObject requestData = parseRequestBody(request);
        if (requestData == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid JSON data", "INVALID_JSON");
            return;
        }

        String username = getStringFromJson(requestData, "username");
        String email = getStringFromJson(requestData, "email");
        String password = getStringFromJson(requestData, "password");
        String firstName = getStringFromJson(requestData, "firstName");
        String lastName = getStringFromJson(requestData, "lastName");
        String phone = getStringFromJson(requestData, "phone");
        String address = getStringFromJson(requestData, "address");

        // Validate required fields
        if (username == null || email == null || password == null ||
            firstName == null || lastName == null) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Required fields are missing", "MISSING_FIELDS");
            return;
        }

        // Validate input formats
        if (!SecurityUtil.isValidUsername(username)) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid username format", "INVALID_USERNAME");
            return;
        }

        if (!SecurityUtil.isValidEmail(email)) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Invalid email format", "INVALID_EMAIL");
            return;
        }

        if (!SecurityUtil.isValidPassword(password)) {
            sendErrorResponse(response, HttpServletResponse.SC_BAD_REQUEST,
                            "Password must be at least 8 characters with uppercase, lowercase, number and special character",
                            "INVALID_PASSWORD");
            return;
        }

        // Check if username or email already exists
        if (userDAO.usernameExists(username)) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT,
                            "Username already exists", "USERNAME_EXISTS");
            return;
        }

        if (userDAO.emailExists(email)) {
            sendErrorResponse(response, HttpServletResponse.SC_CONFLICT,
                            "Email already exists", "EMAIL_EXISTS");
            return;
        }

        // Create new user
        User newUser = new User();
        newUser.setUsername(SecurityUtil.validateAndSanitize(username, 50));
        newUser.setEmail(SecurityUtil.validateAndSanitize(email, 100));
        newUser.setPasswordHash(SecurityUtil.hashPassword(password));
        newUser.setFirstName(SecurityUtil.validateAndSanitize(firstName, 50));
        newUser.setLastName(SecurityUtil.validateAndSanitize(lastName, 50));
        newUser.setPhone(SecurityUtil.validateAndSanitize(phone, 20));
        newUser.setAddress(SecurityUtil.validateAndSanitize(address, 500));
        newUser.setRole(User.UserRole.USER);
        newUser.setMembershipDate(LocalDate.now());
        newUser.setActive(true);

        if (userDAO.createUser(newUser)) {
            // Create session for new user
            SecurityUtil.createUserSession(request, newUser.getUserId(),
                                         newUser.getUsername(), newUser.getRole().name());

            JsonObject responseData = new JsonObject();
            responseData.addProperty("success", true);
            responseData.addProperty("message", "Registration successful");

            JsonObject userData = new JsonObject();
            userData.addProperty("userId", newUser.getUserId());
            userData.addProperty("username", newUser.getUsername());
            userData.addProperty("email", newUser.getEmail());
            userData.addProperty("firstName", newUser.getFirstName());
            userData.addProperty("lastName", newUser.getLastName());
            userData.addProperty("role", newUser.getRole().name());
            userData.addProperty("csrfToken", SecurityUtil.getCSRFToken(request));

            responseData.add("user", userData);

            sendJsonResponse(response, HttpServletResponse.SC_CREATED, responseData);
            LOGGER.info("User registered successfully: " + username);
        } else {
            sendErrorResponse(response, HttpServletResponse.SC_INTERNAL_SERVER_ERROR,
                            "Failed to create user", "REGISTRATION_FAILED");
        }
    }

    /**
     * Handle user logout
     */
    private void handleLogout(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        String username = SecurityUtil.getUsernameFromSession(request);
        SecurityUtil.invalidateSession(request);

        JsonObject responseData = new JsonObject();
        responseData.addProperty("success", true);
        responseData.addProperty("message", "Logout successful");

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
        LOGGER.info("User logged out: " + username);
    }

    /**
     * Handle authentication status check
     */
    private void handleAuthStatus(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        JsonObject responseData = new JsonObject();

        if (SecurityUtil.isAuthenticated(request)) {
            responseData.addProperty("authenticated", true);
            responseData.addProperty("userId", SecurityUtil.getUserIdFromSession(request));
            responseData.addProperty("username", SecurityUtil.getUsernameFromSession(request));
            responseData.addProperty("role", SecurityUtil.getUserRoleFromSession(request));
            responseData.addProperty("csrfToken", SecurityUtil.getCSRFToken(request));
        } else {
            responseData.addProperty("authenticated", false);
        }

        sendJsonResponse(response, HttpServletResponse.SC_OK, responseData);
    }

    // Utility methods
    private JsonObject parseRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }

        try {
            return JsonParser.parseString(sb.toString()).getAsJsonObject();
        } catch (com.google.gson.JsonSyntaxException | IllegalStateException e) {
            LOGGER.log(Level.WARNING, "Failed to parse JSON request body", e);
            return null;
        }
    }

    private String getStringFromJson(JsonObject json, String key) {
        return json.has(key) && !json.get(key).isJsonNull() ? json.get(key).getAsString() : null;
    }

    private void sendJsonResponse(HttpServletResponse response, int statusCode, JsonObject data)
            throws IOException {
        response.setStatus(statusCode);
        try (PrintWriter writer = response.getWriter()) {
            writer.write(data.toString());
        }
    }

    private void sendErrorResponse(HttpServletResponse response, int statusCode,
                                 String message, String errorCode) throws IOException {
        JsonObject errorResponse = new JsonObject();
        errorResponse.addProperty("success", false);
        errorResponse.addProperty("message", message);
        errorResponse.addProperty("errorCode", errorCode);

        sendJsonResponse(response, statusCode, errorResponse);
    }
}
