@echo off
echo ========================================
echo Database Connection Test
echo ========================================

echo.
echo Step 1: Setting up the database...
echo Please run the following in MySQL:
echo.
echo mysql -u root -p
echo (Enter password: Harshalb4u@)
echo.
echo Then copy and paste the contents of quick_setup.sql
echo.

set /p db_ready="Have you set up the database? (y/n): "
if /i "%db_ready%" neq "y" (
    echo Please set up the database first using quick_setup.sql
    pause
    exit /b 1
)

echo.
echo Step 2: Testing Java database connection...

echo Compiling test class...
javac -cp ".;mysql-connector-java-8.0.33.jar" TestConnection.java

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Compilation failed!
    echo Make sure you have:
    echo 1. Java JDK installed
    echo 2. MySQL Connector JAR file in the current directory
    echo.
    echo Download MySQL Connector from:
    echo https://dev.mysql.com/downloads/connector/j/
    pause
    exit /b 1
)

echo Running connection test...
java -cp ".;mysql-connector-java-8.0.33.jar" TestConnection

echo.
echo ========================================
echo Connection test completed!
echo ========================================
pause
