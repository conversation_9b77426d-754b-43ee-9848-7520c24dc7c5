package com.library.filter;

import com.library.util.SecurityUtil;
import com.google.gson.JsonObject;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.logging.Logger;

/**
 * Authentication filter to protect API endpoints
 */
public class AuthenticationFilter implements Filter {
    private static final Logger LOGGER = Logger.getLogger(AuthenticationFilter.class.getName());
    
    // Endpoints that don't require authentication
    private static final Set<String> PUBLIC_ENDPOINTS = new HashSet<>(Arrays.asList(
        "/auth/login",
        "/auth/register",
        "/auth/logout"
    ));
    
    // Admin-only endpoints
    private static final Set<String> ADMIN_ENDPOINTS = new HashSet<>(Arrays.asList(
        "/api/users",
        "/api/books/create",
        "/api/books/update",
        "/api/books/delete",
        "/api/reports",
        "/api/fines/waive"
    ));

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        LOGGER.info("AuthenticationFilter initialized");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String requestURI = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        String path = requestURI.substring(contextPath.length());
        
        LOGGER.info("Processing request: " + path);
        
        // Allow public endpoints
        if (isPublicEndpoint(path)) {
            chain.doFilter(request, response);
            return;
        }
        
        // Check authentication
        if (!SecurityUtil.isAuthenticated(httpRequest)) {
            sendUnauthorizedResponse(httpResponse, "Authentication required");
            return;
        }
        
        // Check admin access for admin endpoints
        if (isAdminEndpoint(path) && !SecurityUtil.isAdmin(httpRequest)) {
            sendForbiddenResponse(httpResponse, "Admin access required");
            return;
        }
        
        // Validate CSRF token for state-changing operations
        if (isStateChangingOperation(httpRequest)) {
            String csrfToken = httpRequest.getHeader("X-CSRF-Token");
            if (!SecurityUtil.validateCSRFToken(httpRequest, csrfToken)) {
                sendForbiddenResponse(httpResponse, "Invalid CSRF token");
                return;
            }
        }
        
        // Check same origin for additional security
        if (!SecurityUtil.isSameOrigin(httpRequest)) {
            sendForbiddenResponse(httpResponse, "Cross-origin request not allowed");
            return;
        }
        
        // Add user information to request attributes
        httpRequest.setAttribute("userId", SecurityUtil.getUserIdFromSession(httpRequest));
        httpRequest.setAttribute("username", SecurityUtil.getUsernameFromSession(httpRequest));
        httpRequest.setAttribute("userRole", SecurityUtil.getUserRoleFromSession(httpRequest));
        
        // Continue with the request
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {
        LOGGER.info("AuthenticationFilter destroyed");
    }
    
    /**
     * Check if endpoint is public (doesn't require authentication)
     */
    private boolean isPublicEndpoint(String path) {
        return PUBLIC_ENDPOINTS.contains(path) || 
               path.startsWith("/static/") || 
               path.startsWith("/css/") || 
               path.startsWith("/js/") || 
               path.startsWith("/images/") ||
               path.equals("/") ||
               path.endsWith(".html") ||
               path.endsWith(".css") ||
               path.endsWith(".js") ||
               path.endsWith(".png") ||
               path.endsWith(".jpg") ||
               path.endsWith(".jpeg") ||
               path.endsWith(".gif") ||
               path.endsWith(".ico");
    }
    
    /**
     * Check if endpoint requires admin access
     */
    private boolean isAdminEndpoint(String path) {
        for (String adminEndpoint : ADMIN_ENDPOINTS) {
            if (path.startsWith(adminEndpoint)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if request is a state-changing operation (requires CSRF protection)
     */
    private boolean isStateChangingOperation(HttpServletRequest request) {
        String method = request.getMethod();
        return "POST".equals(method) || "PUT".equals(method) || 
               "DELETE".equals(method) || "PATCH".equals(method);
    }
    
    /**
     * Send unauthorized response
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) 
            throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        JsonObject jsonResponse = new JsonObject();
        jsonResponse.addProperty("success", false);
        jsonResponse.addProperty("message", message);
        jsonResponse.addProperty("errorCode", "UNAUTHORIZED");
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(jsonResponse.toString());
        }
    }
    
    /**
     * Send forbidden response
     */
    private void sendForbiddenResponse(HttpServletResponse response, String message) 
            throws IOException {
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        JsonObject jsonResponse = new JsonObject();
        jsonResponse.addProperty("success", false);
        jsonResponse.addProperty("message", message);
        jsonResponse.addProperty("errorCode", "FORBIDDEN");
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(jsonResponse.toString());
        }
    }
}
