package com.library.util;

import org.mindrot.jbcrypt.BCrypt;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * Security utility class for password hashing, validation, and session management
 */
public class SecurityUtil {
    
    // Password validation patterns
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$"
    );
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_]{3,20}$"
    );
    
    // BCrypt work factor
    private static final int BCRYPT_ROUNDS = 12;
    
    // Session attribute names
    public static final String SESSION_USER_ID = "userId";
    public static final String SESSION_USERNAME = "username";
    public static final String SESSION_USER_ROLE = "userRole";
    public static final String SESSION_CSRF_TOKEN = "csrfToken";
    
    /**
     * Hash a password using BCrypt
     */
    public static String hashPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.isEmpty()) {
            throw new IllegalArgumentException("Password cannot be null or empty");
        }
        return BCrypt.hashpw(plainPassword, BCrypt.gensalt(BCRYPT_ROUNDS));
    }
    
    /**
     * Verify a password against its hash
     */
    public static boolean verifyPassword(String plainPassword, String hashedPassword) {
        if (plainPassword == null || hashedPassword == null) {
            return false;
        }
        try {
            return BCrypt.checkpw(plainPassword, hashedPassword);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Validate password strength
     */
    public static boolean isValidPassword(String password) {
        if (password == null) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }
    
    /**
     * Validate email format
     */
    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * Validate username format
     */
    public static boolean isValidUsername(String username) {
        if (username == null) {
            return false;
        }
        return USERNAME_PATTERN.matcher(username).matches();
    }
    
    /**
     * Generate a secure random token
     */
    public static String generateSecureToken() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[32];
        random.nextBytes(bytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }
    
    /**
     * Create user session
     */
    public static void createUserSession(HttpServletRequest request, int userId, 
                                       String username, String userRole) {
        HttpSession session = request.getSession(true);
        session.setAttribute(SESSION_USER_ID, userId);
        session.setAttribute(SESSION_USERNAME, username);
        session.setAttribute(SESSION_USER_ROLE, userRole);
        session.setAttribute(SESSION_CSRF_TOKEN, generateSecureToken());
        
        // Set session timeout to 30 minutes
        session.setMaxInactiveInterval(30 * 60);
    }
    
    /**
     * Get user ID from session
     */
    public static Integer getUserIdFromSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (Integer) session.getAttribute(SESSION_USER_ID);
        }
        return null;
    }
    
    /**
     * Get username from session
     */
    public static String getUsernameFromSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute(SESSION_USERNAME);
        }
        return null;
    }
    
    /**
     * Get user role from session
     */
    public static String getUserRoleFromSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute(SESSION_USER_ROLE);
        }
        return null;
    }
    
    /**
     * Check if user is authenticated
     */
    public static boolean isAuthenticated(HttpServletRequest request) {
        return getUserIdFromSession(request) != null;
    }
    
    /**
     * Check if user is admin
     */
    public static boolean isAdmin(HttpServletRequest request) {
        String role = getUserRoleFromSession(request);
        return "ADMIN".equals(role);
    }
    
    /**
     * Check if user is regular user
     */
    public static boolean isUser(HttpServletRequest request) {
        String role = getUserRoleFromSession(request);
        return "USER".equals(role);
    }
    
    /**
     * Invalidate user session
     */
    public static void invalidateSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
    }
    
    /**
     * Validate CSRF token
     */
    public static boolean validateCSRFToken(HttpServletRequest request, String token) {
        if (token == null) {
            return false;
        }
        
        HttpSession session = request.getSession(false);
        if (session != null) {
            String sessionToken = (String) session.getAttribute(SESSION_CSRF_TOKEN);
            return token.equals(sessionToken);
        }
        return false;
    }
    
    /**
     * Get CSRF token from session
     */
    public static String getCSRFToken(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute(SESSION_CSRF_TOKEN);
        }
        return null;
    }
    
    /**
     * Sanitize input to prevent XSS
     */
    public static String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        return input.replaceAll("<", "&lt;")
                   .replaceAll(">", "&gt;")
                   .replaceAll("\"", "&quot;")
                   .replaceAll("'", "&#x27;")
                   .replaceAll("/", "&#x2F;");
    }
    
    /**
     * Validate and sanitize string input
     */
    public static String validateAndSanitize(String input, int maxLength) {
        if (input == null) {
            return null;
        }
        
        // Trim whitespace
        input = input.trim();
        
        // Check length
        if (input.length() > maxLength) {
            input = input.substring(0, maxLength);
        }
        
        // Sanitize
        return sanitizeInput(input);
    }
    
    /**
     * Check if request is from same origin (basic CSRF protection)
     */
    public static boolean isSameOrigin(HttpServletRequest request) {
        String origin = request.getHeader("Origin");
        String referer = request.getHeader("Referer");
        String host = request.getHeader("Host");
        
        if (origin != null) {
            return origin.endsWith("://" + host);
        } else if (referer != null) {
            return referer.startsWith("http://" + host) || referer.startsWith("https://" + host);
        }
        
        // If neither Origin nor Referer is present, allow (for direct API calls)
        return true;
    }
}
