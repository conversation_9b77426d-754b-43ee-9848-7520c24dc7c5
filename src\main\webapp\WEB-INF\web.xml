<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee 
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Library Management System</display-name>
    <description>A comprehensive library management system with smart recommendations</description>

    <!-- Context Parameters -->
    <context-param>
        <param-name>db.driver</param-name>
        <param-value>com.mysql.cj.jdbc.Driver</param-value>
    </context-param>
    <context-param>
        <param-name>db.url</param-name>
        <param-value>**********************************************************************************</param-value>
    </context-param>
    <context-param>
        <param-name>db.username</param-name>
        <param-value>root</param-value>
    </context-param>
    <context-param>
        <param-name>db.password</param-name>
        <param-value>password</param-value>
    </context-param>

    <!-- Authentication Filter -->
    <filter>
        <filter-name>AuthenticationFilter</filter-name>
        <filter-class>com.library.filter.AuthenticationFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>AuthenticationFilter</filter-name>
        <url-pattern>/api/*</url-pattern>
    </filter-mapping>

    <!-- CORS Filter -->
    <filter>
        <filter-name>CorsFilter</filter-name>
        <filter-class>com.library.filter.CorsFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Authentication Servlet -->
    <servlet>
        <servlet-name>AuthServlet</servlet-name>
        <servlet-class>com.library.servlet.AuthServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>AuthServlet</servlet-name>
        <url-pattern>/auth/*</url-pattern>
    </servlet-mapping>

    <!-- Book Management Servlet -->
    <servlet>
        <servlet-name>BookServlet</servlet-name>
        <servlet-class>com.library.servlet.BookServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BookServlet</servlet-name>
        <url-pattern>/api/books/*</url-pattern>
    </servlet-mapping>

    <!-- User Management Servlet -->
    <servlet>
        <servlet-name>UserServlet</servlet-name>
        <servlet-class>com.library.servlet.UserServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>UserServlet</servlet-name>
        <url-pattern>/api/users/*</url-pattern>
    </servlet-mapping>

    <!-- Borrow Management Servlet -->
    <servlet>
        <servlet-name>BorrowServlet</servlet-name>
        <servlet-class>com.library.servlet.BorrowServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>BorrowServlet</servlet-name>
        <url-pattern>/api/borrow/*</url-pattern>
    </servlet-mapping>

    <!-- Search Servlet -->
    <servlet>
        <servlet-name>SearchServlet</servlet-name>
        <servlet-class>com.library.servlet.SearchServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>SearchServlet</servlet-name>
        <url-pattern>/api/search/*</url-pattern>
    </servlet-mapping>

    <!-- Recommendation Servlet -->
    <servlet>
        <servlet-name>RecommendationServlet</servlet-name>
        <servlet-class>com.library.servlet.RecommendationServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>RecommendationServlet</servlet-name>
        <url-pattern>/api/recommendations/*</url-pattern>
    </servlet-mapping>

    <!-- Digital Library Servlet -->
    <servlet>
        <servlet-name>DigitalLibraryServlet</servlet-name>
        <servlet-class>com.library.servlet.DigitalLibraryServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DigitalLibraryServlet</servlet-name>
        <url-pattern>/api/digital/*</url-pattern>
    </servlet-mapping>

    <!-- Report Servlet -->
    <servlet>
        <servlet-name>ReportServlet</servlet-name>
        <servlet-class>com.library.servlet.ReportServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ReportServlet</servlet-name>
        <url-pattern>/api/reports/*</url-pattern>
    </servlet-mapping>

    <!-- Fine Management Servlet -->
    <servlet>
        <servlet-name>FineServlet</servlet-name>
        <servlet-class>com.library.servlet.FineServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FineServlet</servlet-name>
        <url-pattern>/api/fines/*</url-pattern>
    </servlet-mapping>

    <!-- Session Configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
        <cookie-config>
            <http-only>true</http-only>
            <secure>false</secure>
        </cookie-config>
    </session-config>

    <!-- Error Pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/error/404.jsp</location>
    </error-page>
    <error-page>
        <error-code>500</error-code>
        <location>/error/500.jsp</location>
    </error-page>

    <!-- Welcome Files -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <!-- Security Constraints -->
    <security-constraint>
        <web-resource-collection>
            <web-resource-name>Admin Only</web-resource-name>
            <url-pattern>/api/admin/*</url-pattern>
        </web-resource-collection>
        <auth-constraint>
            <role-name>ADMIN</role-name>
        </auth-constraint>
    </security-constraint>

    <!-- MIME Type Mappings -->
    <mime-mapping>
        <extension>pdf</extension>
        <mime-type>application/pdf</mime-type>
    </mime-mapping>
    <mime-mapping>
        <extension>epub</extension>
        <mime-type>application/epub+zip</mime-type>
    </mime-mapping>

</web-app>
