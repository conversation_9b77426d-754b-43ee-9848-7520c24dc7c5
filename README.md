# Library Management System

A comprehensive library management system built with Java Servlets, JDBC, and MySQL. Features include user authentication, book management, borrowing system, digital library, smart recommendations, and administrative reports.

## 🚀 Features

### Core Library Features
- ✅ **User Authentication** - Role-based access control (Admin/User)
- ✅ **Book Management** - CRUD operations for physical books
- 🔄 **Borrowing System** - Book borrowing, returning, and due date tracking
- 🔄 **Fine Management** - Automatic fine calculation for overdue books
- 🔄 **Member Management** - User profile management

### Search & Discovery
- 🔄 **Advanced Search** - Search by title, author, genre, or keyword
- 🔄 **Smart Recommendations** - Genre-based and history-based suggestions
- ✅ **Popular Books** - Trending and most borrowed books

### Digital Library
- 🔄 **Digital Books** - Upload and serve PDF/ePub files
- 🔄 **Online Reading** - Browser-based e-book reader
- 🔄 **Download Support** - Downloadable digital books
- 🔄 **Reading Progress** - Bookmarks and reading tracking

### Administrative Features
- 🔄 **Reports & Analytics** - Usage statistics and reports
- 🔄 **Fine Collection** - Fine management and payment tracking
- 🔄 **User Management** - Admin user management interface

**Legend:** ✅ Implemented | 🔄 Planned/In Progress

## 🛠️ Technology Stack

- **Backend:** Java 11, Servlets, JDBC
- **Database:** MySQL 8.0
- **Security:** BCrypt password hashing, Session-based authentication
- **Build Tool:** Maven
- **Server:** Apache Tomcat 9+

## 📋 Prerequisites

- Java 11 or higher
- MySQL 8.0 or higher
- Apache Tomcat 9.0 or higher
- Maven 3.6 or higher

## 🔧 Setup Instructions

### Quick Setup (Automated)

**For Windows:**
```bash
build_and_deploy.bat
```

**For Linux/Mac:**
```bash
chmod +x build_and_deploy.sh
./build_and_deploy.sh
```

The automated script will:
1. Set up the MySQL database with sample data
2. Compile and package the application
3. Optionally deploy to Tomcat

### Manual Setup

### 1. Database Setup

1. Install MySQL and run the complete setup script:
```bash
mysql -u root -pHarshalb4u@ < setup_database.sql
```

This will create the database, all tables, and insert sample data.

### 2. Application Configuration

The database connection is already configured for your credentials:
- **Username:** root
- **Password:** Harshalb4u@
- **Database:** library_management

### 3. Build and Deploy

1. Build the project:
```bash
mvn clean compile
```

2. Run tests (optional):
```bash
mvn test
```

3. Package the WAR file:
```bash
mvn package
```

4. Deploy to Tomcat:
   - Copy `target/library-management-system.war` to Tomcat's `webapps` directory
   - Start Tomcat server

### 4. Access the Application

- **Application URL:** `http://localhost:8080/library-management-system`
- **API Base URL:** `http://localhost:8080/library-management-system/api`

## 🔐 Default Credentials

### Admin Account
- **Username:** `admin`
- **Password:** `password123`

### Test User Account
- **Username:** `john_doe`
- **Password:** `password123`

## 📚 API Documentation

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/auth/login` | User login |
| POST | `/auth/register` | User registration |
| POST | `/auth/logout` | User logout |
| GET | `/auth/status` | Check authentication status |

### Book Management

| Method | Endpoint | Description | Access |
|--------|----------|-------------|---------|
| GET | `/api/books` | Get all books | All users |
| GET | `/api/books/{id}` | Get book by ID | All users |
| GET | `/api/books/genres` | Get all genres | All users |
| GET | `/api/books/popular` | Get popular books | All users |
| POST | `/api/books` | Create book | Admin only |
| PUT | `/api/books/{id}` | Update book | Admin only |
| DELETE | `/api/books/{id}` | Delete book | Admin only |

### Search & Recommendations

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/search` | Search books |
| GET | `/api/recommendations` | Get recommendations |

## 🏗️ Project Structure

```
src/
├── main/
│   ├── java/com/library/
│   │   ├── dao/           # Data Access Objects
│   │   ├── filter/        # Servlet filters
│   │   ├── model/         # Entity models
│   │   ├── servlet/       # Servlet controllers
│   │   └── util/          # Utility classes
│   └── webapp/
│       ├── WEB-INF/
│       │   └── web.xml    # Servlet configuration
│       └── index.jsp      # Welcome page
├── database/
│   ├── schema.sql         # Database schema
│   └── sample_data.sql    # Sample data
└── pom.xml               # Maven configuration
```

## 🔒 Security Features

- **Password Hashing:** BCrypt with salt
- **Session Management:** Secure session handling
- **CSRF Protection:** Cross-site request forgery prevention
- **Input Validation:** SQL injection and XSS prevention
- **Role-based Access:** Admin and user role separation

## 🧪 Testing

Run unit tests:
```bash
mvn test
```

## 📝 Development Status

This is the initial implementation with core authentication and book management features. Additional features like borrowing system, digital library, and recommendations are planned for future releases.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions, please create an issue in the repository.
