package com.library.dao;

import com.library.model.BorrowRecord;
import com.library.util.DatabaseConnection;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Data Access Object for BorrowRecord operations
 */
public class BorrowRecordDAO {
    private static final Logger LOGGER = Logger.getLogger(BorrowRecordDAO.class.getName());
    private final DatabaseConnection dbConnection;

    public BorrowRecordDAO() {
        this.dbConnection = DatabaseConnection.getInstance();
    }

    /**
     * Create a new borrow record
     */
    public boolean createBorrowRecord(BorrowRecord borrowRecord) {
        String sql = "INSERT INTO borrow_records (user_id, book_id, borrow_date, due_date, status, renewal_count) " +
                    "VALUES (?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            
            stmt.setInt(1, borrowRecord.getUserId());
            stmt.setInt(2, borrowRecord.getBookId());
            stmt.setDate(3, Date.valueOf(borrowRecord.getBorrowDate()));
            stmt.setDate(4, Date.valueOf(borrowRecord.getDueDate()));
            stmt.setString(5, borrowRecord.getStatus().name());
            stmt.setInt(6, borrowRecord.getRenewalCount());
            
            int rowsAffected = stmt.executeUpdate();
            
            if (rowsAffected > 0) {
                try (ResultSet generatedKeys = stmt.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        borrowRecord.setBorrowId(generatedKeys.getInt(1));
                    }
                }
                LOGGER.info("Borrow record created successfully for user: " + borrowRecord.getUserId());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error creating borrow record", e);
        }
        return false;
    }

    /**
     * Get borrow record by ID
     */
    public BorrowRecord getBorrowRecordById(int borrowId) {
        String sql = "SELECT br.*, u.username, u.first_name, u.last_name, b.title, b.author " +
                    "FROM borrow_records br " +
                    "JOIN users u ON br.user_id = u.user_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE br.borrow_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, borrowId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return mapResultSetToBorrowRecord(rs);
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting borrow record by ID: " + borrowId, e);
        }
        return null;
    }

    /**
     * Update borrow record
     */
    public boolean updateBorrowRecord(BorrowRecord borrowRecord) {
        String sql = "UPDATE borrow_records SET due_date = ?, return_date = ?, status = ?, " +
                    "renewal_count = ?, updated_at = CURRENT_TIMESTAMP WHERE borrow_id = ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setDate(1, Date.valueOf(borrowRecord.getDueDate()));
            stmt.setDate(2, borrowRecord.getReturnDate() != null ? 
                        Date.valueOf(borrowRecord.getReturnDate()) : null);
            stmt.setString(3, borrowRecord.getStatus().name());
            stmt.setInt(4, borrowRecord.getRenewalCount());
            stmt.setInt(5, borrowRecord.getBorrowId());
            
            int rowsAffected = stmt.executeUpdate();
            if (rowsAffected > 0) {
                LOGGER.info("Borrow record updated successfully: " + borrowRecord.getBorrowId());
                return true;
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error updating borrow record: " + borrowRecord.getBorrowId(), e);
        }
        return false;
    }

    /**
     * Get user's borrowing history
     */
    public List<BorrowRecord> getUserBorrowHistory(int userId, int offset, int limit) {
        List<BorrowRecord> records = new ArrayList<>();
        String sql = "SELECT br.*, u.username, u.first_name, u.last_name, b.title, b.author " +
                    "FROM borrow_records br " +
                    "JOIN users u ON br.user_id = u.user_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE br.user_id = ? ORDER BY br.borrow_date DESC LIMIT ? OFFSET ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            stmt.setInt(2, limit);
            stmt.setInt(3, offset);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    records.add(mapResultSetToBorrowRecord(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting user borrow history: " + userId, e);
        }
        return records;
    }

    /**
     * Get currently borrowed books by user
     */
    public List<BorrowRecord> getCurrentlyBorrowedBooks(int userId) {
        List<BorrowRecord> records = new ArrayList<>();
        String sql = "SELECT br.*, u.username, u.first_name, u.last_name, b.title, b.author " +
                    "FROM borrow_records br " +
                    "JOIN users u ON br.user_id = u.user_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE br.user_id = ? AND br.status = 'BORROWED' ORDER BY br.due_date";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    records.add(mapResultSetToBorrowRecord(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting currently borrowed books: " + userId, e);
        }
        return records;
    }

    /**
     * Get overdue books
     */
    public List<BorrowRecord> getOverdueBooks() {
        List<BorrowRecord> records = new ArrayList<>();
        String sql = "SELECT br.*, u.username, u.first_name, u.last_name, b.title, b.author " +
                    "FROM borrow_records br " +
                    "JOIN users u ON br.user_id = u.user_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE br.status = 'BORROWED' AND br.due_date < CURDATE() " +
                    "ORDER BY br.due_date";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                records.add(mapResultSetToBorrowRecord(rs));
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting overdue books", e);
        }
        return records;
    }

    /**
     * Get books due soon (within specified days)
     */
    public List<BorrowRecord> getBooksDueSoon(int daysAhead) {
        List<BorrowRecord> records = new ArrayList<>();
        String sql = "SELECT br.*, u.username, u.first_name, u.last_name, b.title, b.author " +
                    "FROM borrow_records br " +
                    "JOIN users u ON br.user_id = u.user_id " +
                    "JOIN books b ON br.book_id = b.book_id " +
                    "WHERE br.status = 'BORROWED' AND br.due_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY) " +
                    "ORDER BY br.due_date";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, daysAhead);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    records.add(mapResultSetToBorrowRecord(rs));
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting books due soon", e);
        }
        return records;
    }

    /**
     * Check if user has already borrowed a specific book
     */
    public boolean hasUserBorrowedBook(int userId, int bookId) {
        String sql = "SELECT COUNT(*) FROM borrow_records WHERE user_id = ? AND book_id = ? AND status = 'BORROWED'";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, userId);
            stmt.setInt(2, bookId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error checking if user borrowed book", e);
        }
        return false;
    }

    /**
     * Get total books borrowed count
     */
    public int getTotalBorrowedBooksCount() {
        String sql = "SELECT COUNT(*) FROM borrow_records";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            if (rs.next()) {
                return rs.getInt(1);
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting total borrowed books count", e);
        }
        return 0;
    }

    /**
     * Get most borrowed books
     */
    public List<Object[]> getMostBorrowedBooks(int limit) {
        List<Object[]> results = new ArrayList<>();
        String sql = "SELECT b.book_id, b.title, b.author, COUNT(br.book_id) as borrow_count " +
                    "FROM books b LEFT JOIN borrow_records br ON b.book_id = br.book_id " +
                    "GROUP BY b.book_id ORDER BY borrow_count DESC LIMIT ?";
        
        try (Connection conn = dbConnection.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setInt(1, limit);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Object[] row = {
                        rs.getInt("book_id"),
                        rs.getString("title"),
                        rs.getString("author"),
                        rs.getInt("borrow_count")
                    };
                    results.add(row);
                }
            }
        } catch (SQLException e) {
            LOGGER.log(Level.SEVERE, "Error getting most borrowed books", e);
        }
        return results;
    }

    /**
     * Map ResultSet to BorrowRecord object
     */
    private BorrowRecord mapResultSetToBorrowRecord(ResultSet rs) throws SQLException {
        BorrowRecord record = new BorrowRecord();
        record.setBorrowId(rs.getInt("borrow_id"));
        record.setUserId(rs.getInt("user_id"));
        record.setBookId(rs.getInt("book_id"));
        
        Date borrowDate = rs.getDate("borrow_date");
        if (borrowDate != null) {
            record.setBorrowDate(borrowDate.toLocalDate());
        }
        
        Date dueDate = rs.getDate("due_date");
        if (dueDate != null) {
            record.setDueDate(dueDate.toLocalDate());
        }
        
        Date returnDate = rs.getDate("return_date");
        if (returnDate != null) {
            record.setReturnDate(returnDate.toLocalDate());
        }
        
        record.setStatus(BorrowRecord.BorrowStatus.valueOf(rs.getString("status")));
        record.setRenewalCount(rs.getInt("renewal_count"));
        
        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            record.setCreatedAt(createdAt.toLocalDateTime());
        }
        
        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            record.setUpdatedAt(updatedAt.toLocalDateTime());
        }
        
        // Additional fields from joins
        try {
            record.setUserName(rs.getString("username"));
            record.setBookTitle(rs.getString("title"));
            record.setBookAuthor(rs.getString("author"));
        } catch (SQLException e) {
            // These fields might not be present in all queries
        }
        
        return record;
    }
}
