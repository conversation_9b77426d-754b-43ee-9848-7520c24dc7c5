package com.library.model;

import java.time.LocalDateTime;

/**
 * DigitalBook model class representing digital versions of books
 */
public class DigitalBook {
    private int digitalBookId;
    private int bookId;
    private String filePath;
    private FileType fileType;
    private long fileSize;
    private int totalPages;
    private LocalDateTime uploadDate;
    private boolean isDownloadable;

    // Additional fields for joined queries
    private String bookTitle;
    private String bookAuthor;
    private String bookGenre;

    // Constructors
    public DigitalBook() {}

    public DigitalBook(int bookId, String filePath, FileType fileType, 
                      long fileSize, int totalPages, boolean isDownloadable) {
        this.bookId = bookId;
        this.filePath = filePath;
        this.fileType = fileType;
        this.fileSize = fileSize;
        this.totalPages = totalPages;
        this.isDownloadable = isDownloadable;
        this.uploadDate = LocalDateTime.now();
    }

    // Getters and Setters
    public int getDigitalBookId() {
        return digitalBookId;
    }

    public void setDigitalBookId(int digitalBookId) {
        this.digitalBookId = digitalBookId;
    }

    public int getBookId() {
        return bookId;
    }

    public void setBookId(int bookId) {
        this.bookId = bookId;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(int totalPages) {
        this.totalPages = totalPages;
    }

    public LocalDateTime getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(LocalDateTime uploadDate) {
        this.uploadDate = uploadDate;
    }

    public boolean isDownloadable() {
        return isDownloadable;
    }

    public void setDownloadable(boolean downloadable) {
        isDownloadable = downloadable;
    }

    public String getBookTitle() {
        return bookTitle;
    }

    public void setBookTitle(String bookTitle) {
        this.bookTitle = bookTitle;
    }

    public String getBookAuthor() {
        return bookAuthor;
    }

    public void setBookAuthor(String bookAuthor) {
        this.bookAuthor = bookAuthor;
    }

    public String getBookGenre() {
        return bookGenre;
    }

    public void setBookGenre(String bookGenre) {
        this.bookGenre = bookGenre;
    }

    // Utility methods
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    public String getFileName() {
        if (filePath == null) return null;
        return filePath.substring(filePath.lastIndexOf('/') + 1);
    }

    public String getMimeType() {
        switch (fileType) {
            case PDF:
                return "application/pdf";
            case EPUB:
                return "application/epub+zip";
            default:
                return "application/octet-stream";
        }
    }

    @Override
    public String toString() {
        return "DigitalBook{" +
                "digitalBookId=" + digitalBookId +
                ", bookId=" + bookId +
                ", filePath='" + filePath + '\'' +
                ", fileType=" + fileType +
                ", fileSize=" + fileSize +
                ", totalPages=" + totalPages +
                ", isDownloadable=" + isDownloadable +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DigitalBook that = (DigitalBook) o;
        return digitalBookId == that.digitalBookId;
    }

    @Override
    public int hashCode() {
        return Integer.hashCode(digitalBookId);
    }

    // Enum for file types
    public enum FileType {
        PDF, EPUB
    }
}
